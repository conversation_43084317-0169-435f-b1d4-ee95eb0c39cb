# nginx.conf

server {
  listen 80;

  # Set the root directory to serve files from
  root /usr/share/nginx/html;
  index index.html index.htm;

  location / {
    # This is the magic for SPAs:
    # Try to serve the requested file directly.
    # If it doesn't exist, fall back to serving /index.html.
    try_files $uri $uri/ /index.html;
  }

  # Optional: Gzip compression for better performance
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}