import { Request, Response } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import pool from './db';

const JWT_SECRET = import.meta.env.VITE_JWT_SECRET || 'secret';
const SALT_ROUNDS = 10;

declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

// Register a new user
export const register = async (req: Request, res: Response) => {
  const { email, password, username } = req.body;
  if (!email || !password || !username) {
    return res.status(400).json({ message: 'Email, password, and username are required' });
  }
  try {
    // Check if user already exists
    const [existingUsers] = await pool.execute(
      'SELECT * FROM users WHERE email = ? OR username = ?',
      [email, username]
    );

    if (Array.isArray(existingUsers) && existingUsers.length > 0) {
      return res.status(409).json({ message: 'User with this email or username already exists' });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, SALT_ROUNDS);
    const userId = uuidv4();

    // Insert new user
    await pool.execute(
      'INSERT INTO users (id,username, email, password_hash) VALUES (?, ?, ?, ?)',
      [userId, username, email, passwordHash]
    );

    // Create profile for the user
    await pool.execute(
      'INSERT INTO profiles (user_id, display_name) VALUES (?, ?)',
      [userId, username]
    );

    // Generate JWT token
    const token = jwt.sign({ id: userId, username }, JWT_SECRET, { expiresIn: '7d' });

    return res.status(201).json({
      status: true,
      message: 'User registered successfully',
      token,
      user: {
        id: userId,
        email,
        username,
        displayName: username,
        bio: 'Welcome to my link page!',
        profileImage: ''
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      status: false,
      message: 'Server error during registration'
    });
  }
};

// Login user
export const login = async (req: Request, res: Response) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ status: false, message: 'Email and password are required' });
  }

  try {
    // Find user by email
    const [users] = await pool.execute(
      'SELECT u.id,u.username,u.password_hash,u.email, p.display_name, p.bio, p.avatar_url FROM users u ' +
      'LEFT JOIN profiles p ON u.id = p.user_id ' +
      'WHERE u.email = ?',
      [email]
    );

    if (!Array.isArray(users) || users.length === 0) {
      return res.status(401).json({ status: false, message: 'Invalid credentials' });
    }
    const user = users[0] as any;

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);

    if (!isPasswordValid) {
      return res.status(401).json({ status: false, message: 'Invalid credentials' });
    }

    // Get user links
    const [links] = await pool.execute(
      'SELECT id, label, url, icon, display_order as `order` FROM links WHERE user_id = ? ORDER BY display_order',
      [user.id]
    );

    // Generate JWT token
    const token = jwt.sign(
      { id: user.id, username: user.username }, JWT_SECRET, { expiresIn: '7d' }
    );

    return res.status(200).json({
      status: true,
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name || user.username,
        bio: user.bio || 'Welcome to my link page!',
        profileImage: user.avatar_url || ''
      },
      links: links || []
    });
  } catch (error) {
    console.log('Login error:', error);
    console.error('Login error:', error);
    return res.status(500).json({ message: 'Server error during login' });
  }
};

export const updateProfile = async (req, res) => {
  try {
    const { displayName, bio } = req.body;
    const userId = req.user.id;
    const profileImage = req.user.profileImage;
    let avatarUrl = profileImage || '';
    if (req.file) {
      const filePath: any = `/uploads/IMAGES_PROFILE/${req.file.filename}`.replace(/\\/g, '/');
      avatarUrl = filePath;
    }

    await pool.execute(
      'UPDATE profiles SET display_name = ?, bio = ?, avatar_url = ? WHERE user_id = ?',
      [displayName, bio, avatarUrl, userId]
    );
    return res.status(200).json({
      status: true,
      message: 'Profile updated successfully',
      user: {
        id: userId,
        displayName,
        bio,
        profileImage
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({ message: 'Server error during update profile' });
  }
};

// Middleware to verify JWT token
export const authenticateToken = (req: Request, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  if (!token) {
    return res.status(401).json({ message: 'Authentication token required' });
  }
  jwt.verify(token, JWT_SECRET, async (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' });
    }
    const { id }: any = user;
    const [users] = await pool.execute(
      'SELECT u.id,u.username,u.email, p.display_name as `displayName`, p.bio, p.avatar_url as `profileImage` FROM users u ' +
      'LEFT JOIN profiles p ON u.id = p.user_id ' +
      'WHERE u.id = ?',
      [id]
    );
    req.user = users[0];
    next();
  });
};