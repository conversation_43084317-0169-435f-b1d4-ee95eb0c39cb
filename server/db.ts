import mysql from 'mysql2/promise';

// Database connection
const pool = mysql.createPool({
  host: import.meta.env.VITE_DB_HOST|| 'localhost',
  user: import.meta.env.VITE_DB_USER|| 'root',
  password: import.meta.env.VITE_DB_PASSWORD|| '',
  database: import.meta.env.VITE_DB_DATABASE|| '',
  // Pool configuration
  waitForConnections: true, // Wait for a connection to become available if the pool is full
  connectionLimit: 10,      // The maximum number of connections to create at once
  queueLimit: 0,   
});
export default pool;