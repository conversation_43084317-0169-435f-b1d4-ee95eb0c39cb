const express = require('express');
const cors = require('cors');
import { register, login, authenticateToken,updateProfile } from './auth';
import { addLink, updateLink, deleteLink, getLinks} from './linkService';
import { getPage } from './pageService';
import {
  getLandingPage,
  upsertLandingPage,
  getPublicLandingPage,
  togglePublishLandingPage,
  deleteLandingPage
} from './landingPageService';

import multer from 'multer';
import path from 'path';
import fs from 'fs';

const app = express();
const PORT = process.env.VITE_PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, `../../public/uploads/IMAGES_PROFILE`);
    //Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) =>{
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'Image-' + uniqueSuffix + ext);
    },
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: function (req, file, cb) {
     const filetypes = /jpeg|jpg|png|gif/;
     const mimetype = filetypes.test(file.mimetype)
     const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
     if (mimetype && extname) {
        return cb(null, true);
     }
     cb(new Error('File type not allowed'));
  }
});

// Auth routes
app.post('/api/auth/register', register);  
app.post('/api/auth/login', login);

// Protected routes for manage Profile
app.get('/api/user/profile', authenticateToken,getLinks);
app.put('/api/user/profile',authenticateToken,upload.single('file'),updateProfile);

// Protected routes for manage Links
app.post('/api/user/links',authenticateToken,addLink);
app.put('/api/user/links/:id',authenticateToken,updateLink);
app.delete('/api/user/links/:id',authenticateToken,deleteLink);

// Protected routes for manage Landing Pages
app.get('/api/user/landing-page', authenticateToken, getLandingPage);
app.post('/api/user/landing-page', authenticateToken, upsertLandingPage);
app.put('/api/user/landing-page', authenticateToken, upsertLandingPage);
app.patch('/api/user/landing-page/toggle-publish', authenticateToken, togglePublishLandingPage);
app.delete('/api/user/landing-page', authenticateToken, deleteLandingPage);

// Public routes for get user page
app.get('/api/pages/:username',getPage);
app.get('/api/landing/:username', getPublicLandingPage);

app.get('/',(req,res)=>{
  res.json('Hello my API')
})

// Start server
app.listen(PORT, () => {
  console.log('Wellcome to Link Hub API!');
  console.log(`Server running on port ${PORT}`);
});