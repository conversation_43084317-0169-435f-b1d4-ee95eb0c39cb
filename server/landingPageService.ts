import { Request, Response } from 'express';
import { landingPageService } from './prisma';

// Get landing page for a user (authenticated)
export const getLandingPage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const landingPage = await landingPageService.getLandingPageByUserId(userId);
    
    if (!landingPage) {
      // Return default structure if no landing page exists
      return res.status(200).json({
        landingPage: null,
        message: 'No landing page found. Create one to get started.'
      });
    }

    return res.status(200).json({ landingPage });
  } catch (error) {
    console.error('Get landing page error:', error);
    return res.status(500).json({ message: 'Server error while fetching landing page' });
  }
};

// Create or update landing page (authenticated)
export const upsertLandingPage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const {
      heroTitle,
      heroSubtitle,
      heroDescription,
      heroImageUrl,
      heroCtaPrimary,
      heroCtaSecondary,
      aboutTitle,
      aboutDescription,
      aboutImageUrl,
      contactEmail,
      contactPhone,
      contactLocation,
      metaTitle,
      metaDescription,
      metaKeywords,
      primaryColor,
      secondaryColor,
      fontFamily,
      customCss,
      isPublished,
      isActive
    } = req.body;

    const landingPage = await landingPageService.upsertLandingPage(userId, {
      heroTitle,
      heroSubtitle,
      heroDescription,
      heroImageUrl,
      heroCtaPrimary,
      heroCtaSecondary,
      aboutTitle,
      aboutDescription,
      aboutImageUrl,
      contactEmail,
      contactPhone,
      contactLocation,
      metaTitle,
      metaDescription,
      metaKeywords,
      primaryColor,
      secondaryColor,
      fontFamily,
      customCss,
      isPublished: isPublished ?? false,
      isActive: isActive ?? true
    });

    return res.status(200).json({
      message: 'Landing page saved successfully',
      landingPage
    });
  } catch (error) {
    console.error('Upsert landing page error:', error);
    return res.status(500).json({ message: 'Server error while saving landing page' });
  }
};

// Get public landing page by username (public endpoint)
export const getPublicLandingPage = async (req: Request, res: Response) => {
  try {
    const { username } = req.params;
    
    if (!username) {
      return res.status(400).json({ message: 'Username is required' });
    }

    const landingPage = await landingPageService.getPublishedLandingPageByUsername(username);
    
    if (!landingPage) {
      return res.status(404).json({ message: 'Landing page not found or not published' });
    }

    return res.status(200).json({ landingPage });
  } catch (error) {
    console.error('Get public landing page error:', error);
    return res.status(500).json({ message: 'Server error while fetching landing page' });
  }
};

// Toggle publish status (authenticated)
export const togglePublishLandingPage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const landingPage = await landingPageService.togglePublishStatus(userId);
    
    return res.status(200).json({
      message: `Landing page ${landingPage.isPublished ? 'published' : 'unpublished'} successfully`,
      landingPage
    });
  } catch (error) {
    console.error('Toggle publish landing page error:', error);
    if (error.message === 'Landing page not found') {
      return res.status(404).json({ message: 'Landing page not found' });
    }
    return res.status(500).json({ message: 'Server error while toggling publish status' });
  }
};

// Delete landing page (authenticated)
export const deleteLandingPage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    await landingPageService.deleteLandingPage(userId);
    
    return res.status(200).json({
      message: 'Landing page deleted successfully'
    });
  } catch (error) {
    console.error('Delete landing page error:', error);
    return res.status(500).json({ message: 'Server error while deleting landing page' });
  }
};
