import { Request, Response } from 'express';
import pool from './db';
import { v4 as uuidv4 } from 'uuid';

export const addLink = async (req :Request, res: Response) => {   
  
  try {
    const { label, url, icon } = req.body;
    const { id } = req.user;
    const uuid = uuidv4();
    
    const [display_order] = await pool.execute(
      'SELECT MAX(display_order) as max_order FROM links WHERE user_id = ?',
      [id]
    );
    const no = display_order[0].max_order ? display_order[0].max_order + 1 : 1;
    await pool.execute(
      'INSERT INTO links (id,user_id, label, url, icon, display_order) VALUES (?, ?, ?, ?, ?, ?)',
      [uuid,id, label, url, icon,no]
    );
    const [link] = await pool.execute(
      'SELECT id, label, url, icon, display_order as `order` FROM links WHERE id = ?',
      [uuid]
    );
        
    return res.status(201).json({ link: link[0] });
  } catch (error) {
    console.log('Add link error:', error);
    console.error('Add link error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

export const updateLink = async (req: Request, res: Response) => {
  try {
    const { id, label, url, icon } = req.body;
    const userId = req.user.id;
    await pool.execute(    
      'UPDATE links SET label = ?, url = ?, icon = ? WHERE id = ? AND user_id = ?',
      [label, url, icon, id, userId]
    );
    const [link] = await pool.execute(
      'SELECT id, label, url, icon, display_order as `order` FROM links WHERE id = ?',
      [id]
    );
    return res.status(200).json({ link: link[0] });
  } catch (error) {
    console.error('Update link error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

export const deleteLink = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    await pool.execute(
      'DELETE FROM links WHERE id = ? AND user_id = ?',
      [id, userId]
    );    
    await pool.end();    
    return res.status(204).end();
  } catch (error) {
    console.error('Delete link error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};
export const getLinks = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const { id } = req.user;
    const [links] = await pool.execute(
      'SELECT id, label, url, icon, display_order as `order` FROM links WHERE user_id = ? ORDER BY display_order',
      [id]
    );    

      console.log('link :' , links)

    return res.status(200).json({ user, links });
  } catch (error) {
    console.error('Get links error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};
