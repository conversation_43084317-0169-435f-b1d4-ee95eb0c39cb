import { Request, Response } from 'express';
import mysql from 'mysql2/promise';
import pool from './db';

export const getPage = async (req: Request, res: Response) => {
  try {
    const { username } = req.params;
    
    console.log('Get page username:', username);

    const sql = `SELECT u.id,u.username,u.email, p.display_name as displayName, p.bio, p.avatar_url as profileImage  
    FROM users u LEFT JOIN profiles p ON u.id = p.user_id 
    WHERE u.username = ? `
    const [user] = await pool.execute(sql,
      [username]
    );

    const [links] = await pool.execute(
      'SELECT id, label, url, icon, display_order as `order` FROM links WHERE user_id = ? ORDER BY display_order',
      [user[0].id]
    );
   

    return res.status(200).json({ user: user[0], links });
  } catch (error) {
    console.error('Get page error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};
