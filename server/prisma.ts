import { PrismaClient } from '@prisma/client';

// Prevent multiple instances of Prisma Client in development
declare global {
  var __prisma: PrismaClient | undefined;
}

const prisma = globalThis.__prisma || new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma;
}

export default prisma;

// Helper functions for landing page operations
export const landingPageService = {
  // Get landing page by user ID
  async getLandingPageByUserId(userId: string) {
    return await prisma.landingPage.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            profile: true,
          }
        }
      }
    });
  },

  // Create or update landing page
  async upsertLandingPage(userId: string, data: any) {
    return await prisma.landingPage.upsert({
      where: { userId },
      update: {
        ...data,
        updatedAt: new Date(),
      },
      create: {
        userId,
        ...data,
      },
    });
  },

  // Get published landing page by username
  async getPublishedLandingPageByUsername(username: string) {
    return await prisma.landingPage.findFirst({
      where: {
        user: { username },
        isPublished: true,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            profile: true,
          }
        }
      }
    });
  },

  // Toggle publish status
  async togglePublishStatus(userId: string) {
    const landingPage = await prisma.landingPage.findUnique({
      where: { userId },
      select: { isPublished: true }
    });

    if (!landingPage) {
      throw new Error('Landing page not found');
    }

    return await prisma.landingPage.update({
      where: { userId },
      data: { isPublished: !landingPage.isPublished }
    });
  },

  // Delete landing page
  async deleteLandingPage(userId: string) {
    return await prisma.landingPage.delete({
      where: { userId }
    });
  }
};
