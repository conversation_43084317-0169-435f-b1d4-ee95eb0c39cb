import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import Index from "./pages/Index";
import Profile from "./pages/Profile";
import AdminDashboard from "./pages/AdminDashboard";
import NotFound from "./pages/NotFound";
import ResetPasswordPage from "./pages/reset-password";
//import LandingPage1 from "./pages/LandingPage1";
//import LandingPage2 from "./pages/LandingPage2";
import LandingPage from "./pages/landing/index";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>     
        
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/admin" element={<AdminDashboard />} />
            <Route path="/:username" element={<Profile />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/landing" element={<LandingPage />} />            
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>        

      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
