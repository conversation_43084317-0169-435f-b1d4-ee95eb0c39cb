
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { set } from 'date-fns';

interface AuthFormProps {
  mode: 'login' | 'signup';
  onToggleMode: () => void;
}

const AuthForm = ({ mode, onToggleMode }: AuthFormProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const { login, signup, isLoading, checkUser, checkEmail } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [registerOK, setRegisterOK] = useState(true);

  const handleCheckUser = async () => {
    const result = await checkUser(username)
    result.status ? setError(null) : setError(result.message);
    setRegisterOK(result.status);
  }
  const handleCheckEmail = async () => {
    if (mode === 'signup') {
      const result = await checkEmail(email)
      result.status ? setError(null) : setError(result.message);
      setRegisterOK(result.status);
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (mode === 'login') {
        const result = await login(email, password);
        if (!result.status) {
          throw new Error(result.message);
        }
        toast({
          title: "Welcome back!",
          description: "You've been successfully logged in.",
        });
      } else {
        /*if (!username) {
          toast({
            title: "Username required",
            description: "Please enter a username.",
            variant: "destructive",
          });
          return;
        }
        if (!email) {
          toast({
            title: "Username required",
            description: "Please enter a email.",
            variant: "destructive",
          });
          return;
        }*/       
        signup(email, password, username);
        toast({
          title: "Account created!",
          description: "Welcome to LinkPage Generator!",
        });

      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }

  };

  const handleForgotPassword = async () => {
    // Implement forgot password logic here

    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter your email.",
        variant: "destructive",
      });
      return;
    }
    await fetch('http://localhost:3000/auth/reset/request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });
  };

  return (
    <Card className="w-full max-w-md mx-auto backdrop-blur-lg bg-white/10 border-white/20">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-white">
          {mode === 'login' ? 'Welcome Back' : 'Create Account'}
        </CardTitle>
        <CardDescription className="text-white/80">
          {mode === 'login'
            ? 'Sign in to manage your link page'
            : 'Start building your personal link page'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>

        <form onSubmit={handleSubmit} className="space-y-4">

          {error && (
            <div className="p-3 text-red-700 bg-red-100 rounded-lg">
              {error}
            </div>
          )}

          {mode === 'signup' && (
            <div>
              <Input
                type="text"
                placeholder="Username"
                value={username}
                pattern="[A-Za-z0-9]+"
                title="Allowed only letters (A-Z, a-z) and numbers (0-9), no spaces"
                onChange={(e) => setUsername(e.target.value)}
                onBlur={handleCheckUser}
                className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
                required
              />
            </div>
          )}     
          <div>
            <Input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onBlur={handleCheckEmail}
              className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
              required
            />
          </div>
          <div>
            <Input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-white text-purple-700 hover:bg-white/90 font-semibold"
            disabled={isLoading || !registerOK}
          >
            {isLoading ? 'Please wait...' : (mode === 'login' ? 'Sign In' : 'Create Account')}
          </Button>

        </form>

        <div className="mt-6 text-center">
          <button
            type="button"
            onClick={onToggleMode}
            className="text-white/80 hover:text-white underline"
          >
            {mode === 'login'
              ? "Don't have an account? Sign up"
              : "Already have an account? Sign in"
            }
          </button>

          <br />

          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-white/80 hover:text-white underline"
          >
            Forgot password
          </button>

        </div>
      </CardContent>
    </Card>
  );
};

export default AuthForm;
