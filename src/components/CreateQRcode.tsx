import React, { useRef } from "react";
//import  QRCode from 'qrcode';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { QRCodeCanvas } from 'qrcode.react';
import html2canvas from 'html2canvas';

function CreateQRcode({ linkurl }: { linkurl: string }) {
   const qrRef = useRef(null);
   const downloadQR = async () => {
      const canvas = await html2canvas(qrRef.current);
      const dataUrl = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = 'qr-code.png';
      link.click();
   };

   return (
      <Card className="max-w-md mx-auto">
         <CardHeader>
            <CardTitle className="text-center">Your QR Code</CardTitle>
         </CardHeader>

         <CardContent ref={qrRef} className="space-y-4">
            <div className="flex justify-center">
               <p className="text-center text-sm text-muted-foreground">
                  {linkurl}
               </p>
            </div>
            <div className="flex justify-center">
               <QRCodeCanvas value={linkurl} size={256} />
            </div>
            <div>
               <p className="text-center text-lg text-muted-foreground">
                  Scan the QR code to visit profile
               </p>
            </div>
         </CardContent>
         <CardContent className="space-y-4">
            <Button onClick={downloadQR} className="w-full">
               Download QR Code
            </Button>
         </CardContent>

      </Card>
   );
}

export default CreateQRcode;
