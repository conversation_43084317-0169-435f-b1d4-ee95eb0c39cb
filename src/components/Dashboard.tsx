
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useAuth } from '@/contexts/AuthContext';
import ProfileSettings from './ProfileSettings';
import LinkManager from './LinkManager';
import LandingPageSettings from './LandingPageSettings';
import { ExternalLink, Settings, Link, User, Briefcase, MessageSquare,AppWindow } from 'lucide-react';
import CreateQRcode from './CreateQRcode';
import clipboard from "clipboardy";

const Dashboard = () => {
  const { user, logout,isLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const publicUrl = `${window.location.origin}/${user?.username}`;  

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'links', label: 'Contact Links', icon: Link },
    { id: 'landing-page', label: 'Landing Page', icon: AppWindow },    
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-4xl font-bold mb-4">Loading...</h1>
        </div>
      </div>
    );
  }

  const handleCopyClipboard = () => {    
    clipboard.write(publicUrl);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-8 items-center justify-center md:flex-row md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Dashboard</h1>
            <p className="text-white/80">Welcome back, {user?.displayName}!</p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
              onClick={() => window.open(publicUrl, '_blank')}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              View Portfolio
            </Button>
            <Button
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
              onClick={logout}
            >
              Logout
            </Button>
          </div>
        </div>

        {/* Public URL Display */}
        <Card className="mb-8 backdrop-blur-lg bg-white/10 border-white/20">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Your Portfolio Link</h3>
                <p className="text-white/80 font-mono text-sm break-all">{publicUrl}</p>
              </div>

            <div>
                <Button
                  onClick={handleCopyClipboard}
                  variant="outline"
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  Copy Link
                </Button>              
            </div>  
              
              
            </div>
            <div className='flex justify-center mt-4'>
              <CreateQRcode linkurl={publicUrl} />
            </div>
          </CardContent>
        </Card>

        {/* Navigation Tabs */}
        <Card className="mb-8 p-3 backdrop-blur-lg bg-white/10 border-white/20">

          <div className="flex flex-wrap gap-4 mb-8">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? 'default' : 'outline'}
                  className={activeTab === tab.id
                    ? 'bg-white text-purple-600'
                    : 'border-white/20 text-white hover:bg-white/10'
                  }
                  onClick={() => setActiveTab(tab.id)}
                >
                  <IconComponent className="w-4 h-4 mr-2" />
                  {tab.label}
                </Button>
              );
            })}
          </div>

          {/* Content */}
          {activeTab === 'profile' && <ProfileSettings />}
          {activeTab === 'links' && <LinkManager />}
          {activeTab === 'landing-page' && <LandingPageSettings />}

        </Card>
        

      </div>
    </div>
  );
};

export default Dashboard;
