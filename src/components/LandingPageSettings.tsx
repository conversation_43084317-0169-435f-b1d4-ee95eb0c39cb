import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Plus, Camera } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { eachMonthOfInterval } from 'date-fns';
import GetProfile, { LandingProfile, updateProfile } from '@/contexts/LandingPageContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

function resizeImage(file, maxWidth, maxHeight) {
   return new Promise((resolve, reject) => {
      const image: any = new Image();
      const reader = new FileReader();
      reader.onload = (e) => {
         image.src = e.target.result;
      };
      image.onload = () => {
         let width = image.width;
         let height = image.height;

         // Resize แบบรักษาสัดส่วน
         if (width > maxWidth || height > maxHeight) {
            if (width > height) {
               height = (height * maxWidth) / width;
               width = maxWidth;
            } else {
               width = (width * maxHeight) / height;
               height = maxHeight;
            }
         }
         const canvas = document.createElement('canvas');
         canvas.width = width;
         canvas.height = height;
         const ctx = canvas.getContext('2d');
         ctx.drawImage(image, 0, 0, width, height);
         canvas.toBlob((blob) => {
            resolve(blob);
         }, file.type, 0.9); // คุณภาพ 90%
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
   });
}

const LandingPageSettings = () => {
   const [imageFile, setImageFile] = useState<File | null>(null);
   const [loading, setLoading] = useState(false);
   const IMG_URL = import.meta.env.VITE_IMG_URL || 'http://localhost:3030/uploads';
   const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
   const [havePage, setHavePage] = useState(false);

   const [newPage, setNewPage] = useState<LandingProfile | null>({
      userID: '',
      name: '',
      title: '',
      bio: '',
      email: '',
      phone: '',
      location: '',
      github: '',
      linkedin: '',
      twitter: '',
      image: '',
      file: null
   });

   const [formData, setFormData] = useState<LandingProfile>({
      userID: '',
      name: '',
      title: '',
      bio: '',
      email: '',
      phone: '',
      location: '',
      github: '',
      linkedin: '',
      twitter: '',
      image: '',
      file: null
   });

   const profile = async () => {
      const { status, message, profile } = await GetProfile();

      if (status) {
         setFormData({
            userID: profile?.userID || '',
            name: profile?.name || '',
            title: profile?.title || '',
            bio: profile?.bio || '',
            email: profile?.email || '',
            phone: profile?.phone || '',
            location: profile?.location || '',
            github: profile?.github || '',
            linkedin: profile?.linkedin || '',
            twitter: profile?.twitter || '',
            image: profile?.image ? `${IMG_URL}/${profile?.image}` : '',
            file: profile?.file || null
         });
         setHavePage(true);
      }
   }

   useEffect(() => {
      setLoading(true);
      profile();
      setLoading(false);
   }, []);

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      const formDataProfile = new FormData();
      formDataProfile.append('userID', newPage?.userID || '');
      formDataProfile.append('name', newPage.name);
      formDataProfile.append('title', newPage.title);
      formDataProfile.append('bio', newPage.bio);
      formDataProfile.append('email', newPage.email);
      formDataProfile.append('phone', newPage.phone);
      formDataProfile.append('location', newPage.location);
      formDataProfile.append('github', newPage.github);
      formDataProfile.append('linkedin', newPage.linkedin);
      formDataProfile.append('twitter', newPage.twitter);
      formDataProfile.append('image', newPage.image);
      formDataProfile.append('file', newPage.file || null);
      if (imageFile) {
         const resize: any = await resizeImage(imageFile, 800, 800);
         formDataProfile.append('profileImage', imageFile.name);
         formDataProfile.append('image', resize, imageFile.name);
      }
      updateProfile(formDataProfile);
      toast({
         title: "Profile updated",
         description: "Your profile has been successfully updated.",
      });
   };

   const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
         setFormData({ ...formData, file });
         setImageFile(file);
         const reader = new FileReader();
         reader.onload = (event) => {
            if (event.target?.result) {
               setFormData({ ...formData, image: event.target.result as string });
            }
         };
         reader.readAsDataURL(file);
      }
   };

   if (loading) {
      return (
         <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
            <div className="text-center text-white">
               <h1 className="text-4xl font-bold mb-4">Loading...</h1>
            </div>
         </div>
      );
   } else {
      return (
         <div className="w-full">
            <div className="flex items-center justify-between py-6">
               <h2 className="text-2xl font-bold text-white">Your Landing Page </h2>
               <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                  <DialogTrigger asChild>
                     <Button className="bg-white text-purple-600 hover:bg-white/90">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Page
                     </Button>
                  </DialogTrigger>
                  <DialogContent className="bg-white">
                     <DialogHeader>
                        <DialogTitle>Add New Page</DialogTitle>
                     </DialogHeader>
                     <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="flex flex-col items-center space-y-4">
                           <div className="relative">
                              <Avatar className="w-24 h-24">
                                 <AvatarImage src={newPage.image} alt="Profile" />
                                 <AvatarFallback className="text-2xl">
                                    {newPage.name ? newPage.name.charAt(0).toUpperCase() : ''}
                                 </AvatarFallback>
                              </Avatar>
                              <label
                                 htmlFor="image-upload"
                                 className="absolute bottom-0 right-0 bg-white text-purple-600 rounded-full p-2 cursor-pointer hover:bg-white/90 transition-colors"
                              >
                                 <Camera className="w-4 h-4" />
                              </label>
                              <input
                                 id="image-upload"
                                 type="file"
                                 accept="image/*"
                                 onChange={handleImageUpload}
                                 className="hidden"
                              />
                           </div>
                           <p className="text-white/60 text-sm text-center">
                              Click the camera icon to upload a new profile picture
                           </p>
                        </div>
                        <div>
                           <Input
                              placeholder="Full Name"
                              value={newPage.name}
                              onChange={(e) => setNewPage((s) => ({ ...s, name: e.target.value }))}
                              required
                           />
                        </div>
                        <div>
                           <Input
                              placeholder="Professional Title"
                              value={newPage.title}
                              onChange={(e) => setNewPage((s) => ({ ...s, title: e.target.value }))}
                              required
                           />
                        </div>
                        <div>
                           <Input
                              placeholder="Bio"
                              value={newPage.bio}
                              onChange={(e) => setNewPage((s) => ({ ...s, bio: e.target.value }))}
                              required
                           />
                        </div>
                        <div>
                           <Input
                              placeholder="Email"
                              value={newPage.email}
                              onChange={(e) => setNewPage((s) => ({ ...s, email: e.target.value }))}
                              required
                           />
                        </div>
                        <div>
                           <Input
                              placeholder="Phone"
                              value={newPage.phone}
                              onChange={(e) => setNewPage((s) => ({ ...s, phone: e.target.value }))}
                              required
                           />
                        </div>
                        <div>
                           <Input
                              placeholder="Location"
                              value={newPage.location}
                              onChange={(e) => setNewPage((s) => ({ ...s, location: e.target.value }))}
                              required
                           />
                        </div>

                        <Button type="submit" className="w-full">
                           Add Page
                        </Button>
                     </form>
                  </DialogContent>
               </Dialog>

            </div>

            {havePage === false ? (
               <Card className="backdrop-blur-lg bg-white/10 border-white/20">
                  <CardContent className="p-8 text-center">
                     <p className="text-white/80 mb-4">No page added yet.</p>
                     <p className="text-white/60 text-sm">Click the "Add Page" button to get started!</p>
                  </CardContent>
               </Card>
            ) : (
               <Card className="backdrop-blur-lg bg-white/10 border-white/20">
                  <CardContent className="p-8 text-center">


                     <div className="flex flex-col items-center space-y-4">
                        <div className="relative">
                           <Avatar className="w-24 h-24">
                              <AvatarImage src={formData.image} alt="Profile" />
                              <AvatarFallback className="text-2xl">
                                 {formData.name ? formData.name.charAt(0).toUpperCase() : ''}
                              </AvatarFallback>
                           </Avatar>
                        </div>
                     </div>
                     <p className="text-white/80 mb-4">{formData.name}</p>
                     <p className="text-white/60 text-sm">{formData.title}</p>
                     <p className="text-white/60 text-sm">{formData.bio}</p>
                     
                     <div className="flex justify-center my-6">
                        <a href={`/landing/${formData.userID}`} target="_blank" rel="noreferrer">
                           <Button className="w-full bg-white text-purple-600 hover:bg-white/90">
                              View Page
                           </Button>
                        </a>
                     </div>

                  </CardContent>
               </Card>
            )}

         </div>
      );
   }
};
export default LandingPageSettings;