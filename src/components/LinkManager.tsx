
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from '@/contexts/AuthContext';
import { Plus,MapPin, Edit2, Trash2, GripVertical, ExternalLink, Mail, Instagram, Youtube, Linkedin, Github, Twitter, Facebook, Dribbble } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const iconOptions = [
  { value: 'external-link', label: 'Website', icon: ExternalLink },
  { value: 'mail', label: 'Email', icon: Mail },
  { value: 'instagram', label: 'Instagram', icon: Instagram },
  { value: 'youtube', label: 'YouTube', icon: Youtube },
  { value: 'linkedin', label: 'LinkedIn', icon: Linkedin },
  { value: 'github', label: 'GitHub', icon: Github },
  { value: 'twitter', label: 'Twitter', icon: Twitter },
  { value: 'facebook', label: 'Facebook', icon: Facebook },
  { value: 'dribbble', label: 'Dribbble', icon: Dribbble }
];

const LinkManager = () => {
  const { links, addLink, updateLink, deleteLink } = useAuth();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    id: '',
    label: '',
    url: '',
    icon: 'external-link'
  });

  const getIcon = (iconName: string) => {
    const iconOption = iconOptions.find(option => option.value === iconName);
    return iconOption ? iconOption.icon : ExternalLink;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.label || !formData.url) {
      toast({
        title: "Missing information",
        description: "Please fill in all fields.",
        variant: "destructive",
      });
      return;
    }

    if (editingLink) {
      updateLink(editingLink, formData);
      toast({
        title: "Link updated",
        description: "Your link has been successfully updated.",
      });
      setEditingLink(null);
    } else {
      addLink(formData);
      toast({
        title: "Link added",
        description: "Your new link has been added successfully.",
      });
      setIsAddDialogOpen(false);
    }

    setFormData({id: '', label: '', url: '', icon: 'external-link' });
  };

  const handleEdit = (link: any) => {
    setEditingLink(link.id);
    setFormData({
      id: link.id,
      label: link.label,
      url: link.url,
      icon: link.icon
    });
  };

  const handleDelete = (linkId: string) => {
    deleteLink(linkId);
    toast({
      title: "Link deleted",
      description: "Your link has been removed.",
    });
  };

  const sortedLinks = [...links].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Your Links</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-white text-purple-600 hover:bg-white/90">
              <Plus className="w-4 h-4 mr-2" />
              Add Link
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white">
            <DialogHeader>
              <DialogTitle>Add New Link</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <Select
                  value={formData.icon}
                  onValueChange={(value) => setFormData({ ...formData, icon: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an icon" />
                  </SelectTrigger>
                  <SelectContent>
                    {iconOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center">
                          <option.icon className="w-4 h-4 mr-2" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Input
                  placeholder="Link Label (e.g., My Website)"
                  value={formData.label}
                  onChange={(e) => setFormData({ ...formData, label: e.target.value })}
                  required
                />
              </div>
              <div>
                <Input
                  placeholder="URL (e.g., https://example.com)"
                  value={formData.url}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  required
                />
              </div>

              <Button type="submit" className="w-full">
                Add Link
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {sortedLinks.length === 0 ? (
        <Card className="backdrop-blur-lg bg-white/10 border-white/20">
          <CardContent className="p-8 text-center">
            <p className="text-white/80 mb-4">No links added yet.</p>
            <p className="text-white/60 text-sm">Click the "Add Link" button to get started!</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {sortedLinks.map((link) => {
            const IconComponent = getIcon(link.icon);
            return (
              <Card key={link.id} className="backdrop-blur-lg bg-white/10 border-white/20">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <GripVertical className="w-5 h-5 text-white/40" />
                      <IconComponent className="w-5 h-5 text-white" />
                      <div>
                        <h3 className="font-semibold text-white">{link.label}</h3>                        
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-white/20 text-white hover:bg-white/10"
                        onClick={() => handleEdit(link)}
                      >
                        <Edit2 className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-red-400/20 text-red-400 hover:bg-red-400/10"
                        onClick={() => handleDelete(link.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingLink} onOpenChange={() => setEditingLink(null)}>
        <DialogContent className="bg-white">
          <DialogHeader>
            <DialogTitle>Edit Link</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                placeholder="Link Label"
                value={formData.label}
                onChange={(e) => setFormData({ ...formData, label: e.target.value })}
                required
              />
            </div>
            <div>
              <Input
                placeholder="URL"
                value={formData.url}
                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                required
              />
            </div>
            <div>
              <Select
                value={formData.icon}
                onValueChange={(value) => setFormData({ ...formData, icon: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an icon" />
                </SelectTrigger>
                <SelectContent>
                  {iconOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center">
                        <option.icon className="w-4 h-4 mr-2" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button type="submit" className="w-full">
              Update Link
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LinkManager;
