import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from '@/contexts/AuthContext';
import { Camera } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

function resizeImage(file, maxWidth, maxHeight) {
  return new Promise((resolve, reject) => {
    const image: any = new Image();
    const reader = new FileReader();
    reader.onload = (e) => {
      image.src = e.target.result;
    };
    image.onload = () => {
      let width = image.width;
      let height = image.height;

      // Resize แบบรักษาสัดส่วน
      if (width > maxWidth || height > maxHeight) {
        if (width > height) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        } else {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(image, 0, 0, width, height);
      canvas.toBlob((blob) => {
        resolve(blob);
      }, file.type, 0.9); // คุณภาพ 90%
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

const ProfileSettings = () => {
  const { user, updateProfile,isLoading } = useAuth();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const IMG_URL = import.meta.env.VITE_IMG_URL || 'http://localhost:3030/uploads';
 
  const [formData, setFormData] = useState({
    displayName: user?.displayName || '' ,
    bio: user?.bio || '' ,
    profileImage: user?.profileImage ? `${IMG_URL}/${user?.profileImage}` : '',
    username: user?.username  || '',
    image:null
  });
   
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();    
    const formDataProfile = new FormData();
    formDataProfile.append('display_name', formData.displayName);
    formDataProfile.append('bio', formData.bio);  
    if (imageFile) {
      const resize: any = await resizeImage(imageFile, 800, 800);
      formDataProfile.append('profileImage', imageFile.name);
      formDataProfile.append('image', resize, imageFile.name);
    }    
    updateProfile(formDataProfile);
    toast({
      title: "Profile updated",
      description: "Your profile has been successfully updated.",
    });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const image = e.target.files?.[0];
    if (image) {
      setFormData({ ...formData, image });
      setImageFile(image);      
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData({ ...formData, profileImage: event.target.result as string });
        }
      };
      reader.readAsDataURL(image);
    }
  };
  
  return (
    <div className="w-full">
      <Card className="backdrop-blur-lg bg-white/10 border-white/20">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-white">Profile Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Profile Image */}
            
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <Avatar className="w-24 h-24">
                  <AvatarImage src={formData.profileImage} alt="Profile" />
                  <AvatarFallback className="text-2xl">
                    {formData.displayName ? formData.displayName.charAt(0).toUpperCase():''}
                  </AvatarFallback>
                </Avatar>
                <label 
                  htmlFor="image-upload"
                  className="absolute bottom-0 right-0 bg-white text-purple-600 rounded-full p-2 cursor-pointer hover:bg-white/90 transition-colors"
                >
                  <Camera className="w-4 h-4" />
                </label>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
              <p className="text-white/60 text-sm text-center">
                Click the camera icon to upload a new profile picture
              </p>
            </div>

            {/* Display Name */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Full Name / Display Name *
              </label>
              <Input
                value={formData.displayName}
                onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
                placeholder="Enter your full name"
                required
              />
            </div>

            {/* Bio */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Professional Bio
              </label>
              <Textarea
                value={formData.bio}
                onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                className="bg-white/10 border-white/20 text-white placeholder:text-white/60 min-h-24"
                placeholder="Tell potential clients about your skills, experience, and what makes you unique..."
                maxLength={300}
              />
              <p className="text-white/60 text-sm mt-1">
                {formData.bio.length}/300 characters
              </p>
            </div>

            {/* Username (readonly) */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Username
              </label>
              <Input
                value={formData.username}
                readOnly
                className="bg-white/5 border-white/10 text-white/60 cursor-not-allowed"
              />
              <p className="text-white/60 text-sm mt-1">
                Your username cannot be changed
              </p>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-white text-purple-600 hover:bg-white/90 font-semibold"
            >
              Update Profile
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSettings;
