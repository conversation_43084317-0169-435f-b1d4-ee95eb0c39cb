// Language: TypeScript JSX
// FILE: src/components/ProjectManager.tsx

import React, { useState, useRef } from "react";

export type Project = {
  id: string;
  title: string;
  name?: string;
  image?: string; // url or data URL
  description?: string;
};

export default function ProjectManager({
  projects,
  onChange,
}: {
  projects: Project[];
  onChange: (projects: Project[]) => void;
}) {
  const [open, setOpen] = useState(false);
  const [editing, setEditing] = useState<Project | null>(null);
  const [form, setForm] = useState<Omit<Project, "id">>({ title: "", name: "", image: "", description: "" });
  const fileRef = useRef<HTMLInputElement | null>(null);

  function openNew() {
    setEditing(null);
    setForm({ title: "", name: "", image: "", description: "" });
    setOpen(true);
  }

  function openEdit(p: Project) {
    setEditing(p);
    setForm({ title: p.title, name: p.name || "", image: p.image || "", description: p.description || "" });
    setOpen(true);
  }

  function handleFile(e: React.ChangeEvent<HTMLInputElement>) {
    const f = e.target.files?.[0];
    if (!f) return;
    const reader = new FileReader();
    reader.onload = () => {
      setForm((s) => ({ ...s, image: String(reader.result) }));
    };
    reader.readAsDataURL(f);
  }

  function handleSave(e?: React.FormEvent) {
    e?.preventDefault();
    if (!form.title.trim()) return alert("Title required");
    if (editing) {
      const updated = projects.map((p) => (p.id === editing.id ? { ...p, ...form } : p));
      onChange(updated);
    } else {
      const newProject: Project = {
        id: String(Date.now()) + "-" + Math.random().toString(36).slice(2, 9),
        ...form,
      };
      onChange([newProject, ...projects]);
    }
    setOpen(false);
  }

  function handleDelete(p: Project) {
    if (!confirm(`Delete project "${p.title}"?`)) return;
    onChange(projects.filter((x) => x.id !== p.id));
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Projects</h3>
        <button onClick={openNew} className="px-3 py-1 bg-indigo-600 rounded text-sm">Add Project</button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {projects.map((p) => (
          <article key={p.id} className="bg-white/5 rounded-lg overflow-hidden">
            <div className="h-40 bg-gray-700 flex items-center justify-center">
              {p.image ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img src={p.image} alt={p.title} className="w-full h-40 object-cover" />
              ) : (
                <div className="text-sm text-white/60">No image</div>
              )}
            </div>
            <div className="p-3">
              <div className="flex items-start justify-between gap-3">
                <div>
                  <div className="font-semibold">{p.title}</div>
                  {p.name && <div className="text-xs text-white/70">{p.name}</div>}
                </div>

                <div className="flex flex-col items-end gap-2">
                  <div className="flex gap-2">
                    <button onClick={() => openEdit(p)} className="px-2 py-1 bg-white/10 rounded text-xs">Edit</button>
                    <button onClick={() => handleDelete(p)} className="px-2 py-1 bg-red-600 rounded text-xs">Delete</button>
                  </div>
                </div>
              </div>

              {p.description && <div className="text-sm text-white/80 mt-2">{p.description}</div>}
            </div>
          </article>
        ))}
      </div>

      {/* Modal */}
      {open && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
          <form
            onSubmit={handleSave}
            className="w-full max-w-2xl bg-slate-900 p-6 rounded-lg"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold">{editing ? "Edit Project" : "Add Project"}</h4>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setOpen(false);
                  }}
                  className="px-2 py-1 bg-white/10 rounded"
                >
                  Close
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <input
                className="w-full px-3 py-2 bg-transparent border border-white/10 rounded"
                placeholder="Title"
                value={form.title}
                onChange={(e) => setForm((s) => ({ ...s, title: e.target.value }))}
                required
              />
              <input
                className="w-full px-3 py-2 bg-transparent border border-white/10 rounded"
                placeholder="Name (client or project owner)"
                value={form.name}
                onChange={(e) => setForm((s) => ({ ...s, name: e.target.value }))}
              />

              <div className="flex gap-2">
                <input
                  className="flex-1 px-3 py-2 bg-transparent border border-white/10 rounded"
                  placeholder="Image URL"
                  value={form.image}
                  onChange={(e) => setForm((s) => ({ ...s, image: e.target.value }))}
                />
                <label className="px-3 py-2 bg-white/5 rounded cursor-pointer text-sm">
                  Upload
                  <input ref={fileRef} type="file" accept="image/*" onChange={handleFile} className="hidden" />
                </label>
              </div>

              {form.image && (
                // eslint-disable-next-line @next/next/no-img-element
                <img src={form.image} alt="preview" className="w-full h-40 object-cover rounded" />
              )}

              <textarea
                className="w-full px-3 py-2 bg-transparent border border-white/10 rounded h-28"
                placeholder="Description"
                value={form.description}
                onChange={(e) => setForm((s) => ({ ...s, description: e.target.value }))}
              />

              <div className="flex items-center gap-3">
                <button type="submit" className="px-4 py-2 bg-indigo-600 rounded">Save</button>
                <button
                  type="button"
                  onClick={() => {
                    setOpen(false);
                  }}
                  className="px-4 py-2 bg-white/10 rounded"
                >
                  Cancel
                </button>
                {editing && (
                  <button
                    type="button"
                    onClick={() => {
                      if (!editing) return;
                      if (!confirm("Delete this project?")) return;
                      onChange(projects.filter((x) => x.id !== editing.id));
                      setOpen(false);
                    }}
                    className="ml-auto px-3 py-1 bg-red-600 rounded text-sm"
                  >
                    Delete
                  </button>
                )}
              </div>
            </div>
          </form>

          {/* click outside to close */}
          <div className="absolute inset-0" onClick={() => setOpen(false)} />
        </div>
      )}
    </div>
  );
}