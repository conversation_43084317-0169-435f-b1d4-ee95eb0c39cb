
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Plus, Edit, Trash2, Star, GripVertical } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Testimonial {
  id: string;
  clientName: string;
  clientImage?: string;
  testimonialText: string;
  order: number;
}

const TestimonialsManager = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([
    {
      id: '1',
      clientName: '<PERSON>',
      clientImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b784?w=150',
      testimonialText: 'Excellent work! The website exceeded our expectations and was delivered on time.',
      order: 1
    },
    {
      id: '2',
      clientName: 'Mike Chen',
      testimonialText: 'Professional, creative, and responsive. I highly recommend their services.',
      order: 2
    }
  ]);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingTestimonial, setEditingTestimonial] = useState<Testimonial | null>(null);
  const [formData, setFormData] = useState({
    clientName: '',
    clientImage: '',
    testimonialText: ''
  });

  const handleAddTestimonial = () => {
    if (!formData.clientName || !formData.testimonialText) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    const newTestimonial: Testimonial = {
      id: Date.now().toString(),
      clientName: formData.clientName,
      clientImage: formData.clientImage || undefined,
      testimonialText: formData.testimonialText,
      order: testimonials.length + 1
    };

    setTestimonials([...testimonials, newTestimonial]);
    setFormData({ clientName: '', clientImage: '', testimonialText: '' });
    setIsAddModalOpen(false);
    
    toast({
      title: "Testimonial added",
      description: "The testimonial has been successfully added.",
    });
  };

  const handleEditTestimonial = (testimonial: Testimonial) => {
    setEditingTestimonial(testimonial);
    setFormData({
      clientName: testimonial.clientName,
      clientImage: testimonial.clientImage || '',
      testimonialText: testimonial.testimonialText
    });
  };

  const handleUpdateTestimonial = () => {
    if (!editingTestimonial) return;

    const updatedTestimonials = testimonials.map(testimonial =>
      testimonial.id === editingTestimonial.id
        ? {
            ...testimonial,
            clientName: formData.clientName,
            clientImage: formData.clientImage || undefined,
            testimonialText: formData.testimonialText
          }
        : testimonial
    );

    setTestimonials(updatedTestimonials);
    setEditingTestimonial(null);
    setFormData({ clientName: '', clientImage: '', testimonialText: '' });
    
    toast({
      title: "Testimonial updated",
      description: "The testimonial has been successfully updated.",
    });
  };

  const handleDeleteTestimonial = (id: string) => {
    setTestimonials(testimonials.filter(testimonial => testimonial.id !== id));
    toast({
      title: "Testimonial deleted",
      description: "The testimonial has been removed.",
    });
  };

  return (
    <div className="max-w-4xl">
      <Card className="backdrop-blur-lg bg-white/10 border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-white">Testimonials Management</CardTitle>
            <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
              <DialogTrigger asChild>
                <Button className="bg-white text-purple-600 hover:bg-white/90">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Testimonial
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-card border-border">
                <DialogHeader>
                  <DialogTitle>Add New Testimonial</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="clientName">Client Name *</Label>
                    <Input
                      id="clientName"
                      value={formData.clientName}
                      onChange={(e) => setFormData({ ...formData, clientName: e.target.value })}
                      placeholder="Enter client name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="clientImage">Client Image URL (optional)</Label>
                    <Input
                      id="clientImage"
                      value={formData.clientImage}
                      onChange={(e) => setFormData({ ...formData, clientImage: e.target.value })}
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                  <div>
                    <Label htmlFor="testimonialText">Testimonial Text *</Label>
                    <Textarea
                      id="testimonialText"
                      value={formData.testimonialText}
                      onChange={(e) => setFormData({ ...formData, testimonialText: e.target.value })}
                      placeholder="Enter the testimonial text"
                      rows={4}
                      maxLength={300}
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      {formData.testimonialText.length}/300 characters
                    </p>
                  </div>
                  <Button onClick={handleAddTestimonial} className="w-full">
                    Add Testimonial
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testimonials.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-white/60">No testimonials yet. Add your first client testimonial!</p>
              </div>
            ) : (
              testimonials
                .sort((a, b) => a.order - b.order)
                .map((testimonial) => (
                  <Card key={testimonial.id} className="bg-white/5 border-white/10">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex items-center text-white/40">
                          <GripVertical className="w-4 h-4" />
                        </div>
                        
                        <Avatar className="w-12 h-12 flex-shrink-0">
                          <AvatarImage src={testimonial.clientImage} alt={testimonial.clientName} />
                          <AvatarFallback className="bg-white/20 text-white">
                            {testimonial.clientName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h3 className="text-white font-semibold">{testimonial.clientName}</h3>
                              <div className="flex items-center mb-2">
                                {[...Array(5)].map((_, i) => (
                                  <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                ))}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2 ml-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditTestimonial(testimonial)}
                                className="text-white/60 hover:text-white hover:bg-white/10"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteTestimonial(testimonial.id)}
                                className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <p className="text-white/80 italic">"{testimonial.testimonialText}"</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Modal */}
      <Dialog open={!!editingTestimonial} onOpenChange={() => setEditingTestimonial(null)}>
        <DialogContent className="bg-card border-border">
          <DialogHeader>
            <DialogTitle>Edit Testimonial</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-clientName">Client Name *</Label>
              <Input
                id="edit-clientName"
                value={formData.clientName}
                onChange={(e) => setFormData({ ...formData, clientName: e.target.value })}
                placeholder="Enter client name"
              />
            </div>
            <div>
              <Label htmlFor="edit-clientImage">Client Image URL (optional)</Label>
              <Input
                id="edit-clientImage"
                value={formData.clientImage}
                onChange={(e) => setFormData({ ...formData, clientImage: e.target.value })}
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <div>
              <Label htmlFor="edit-testimonialText">Testimonial Text *</Label>
              <Textarea
                id="edit-testimonialText"
                value={formData.testimonialText}
                onChange={(e) => setFormData({ ...formData, testimonialText: e.target.value })}
                placeholder="Enter the testimonial text"
                rows={4}
                maxLength={300}
              />
              <p className="text-sm text-muted-foreground mt-1">
                {formData.testimonialText.length}/300 characters
              </p>
            </div>
            <Button onClick={handleUpdateTestimonial} className="w-full">
              Update Testimonial
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TestimonialsManager;
