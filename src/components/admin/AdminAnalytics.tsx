
import React from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>hart, Pie, Cell } from 'recharts';

interface AdminAnalyticsProps {
  data: {
    userGrowth: Array<{ date: string; users: number }>;
  };
}

const AdminAnalytics: React.FC<AdminAnalyticsProps> = ({ data }) => {
  // Mock data for additional charts
  const linksCreatedData = [
    { date: '2024-01-01', links: 45 },
    { date: '2024-01-02', links: 52 },
    { date: '2024-01-03', links: 38 },
    { date: '2024-01-04', links: 67 },
    { date: '2024-01-05', links: 73 },
    { date: '2024-01-06', links: 89 },
    { date: '2024-01-07', links: 94 }
  ];

  const clicksData = [
    { date: '2024-01-01', clicks: 12543 },
    { date: '2024-01-02', clicks: 13672 },
    { date: '2024-01-03', clicks: 11234 },
    { date: '2024-01-04', clicks: 15678 },
    { date: '2024-01-05', clicks: 17891 },
    { date: '2024-01-06', clicks: 19234 },
    { date: '2024-01-07', clicks: 21456 }
  ];

  const deviceData = [
    { name: 'Mobile', value: 65, color: '#8b5cf6' },
    { name: 'Desktop', value: 30, color: '#06b6d4' },
    { name: 'Tablet', value: 5, color: '#10b981' }
  ];

  const chartConfig = {
    users: {
      label: "Users",
      color: "hsl(var(--chart-1))",
    },
    links: {
      label: "Links",
      color: "hsl(var(--chart-2))",
    },
    clicks: {
      label: "Clicks",
      color: "hsl(var(--chart-3))",
    },
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* User Growth Chart */}
      <Card className="backdrop-blur-lg bg-black border-white/20">
        <CardHeader>
          <CardTitle className="text-white">New Users per Day</CardTitle>
          <CardDescription className="text-white/70">
            Daily user registration trend
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.userGrowth}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis 
                  dataKey="date" 
                  stroke="rgba(255,255,255,0.7)"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis stroke="rgba(255,255,255,0.7)" fontSize={12} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line 
                  type="monotone" 
                  dataKey="users" 
                  stroke="#8b5cf6" 
                  strokeWidth={2}
                  dot={{ fill: "#8b5cf6", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Links Created Chart */}
      <Card className="backdrop-blur-lg bg-black border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Links Created per Day</CardTitle>
          <CardDescription className="text-white/70">
            Daily number of links created
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={linksCreatedData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis 
                  dataKey="date" 
                  stroke="rgba(255,255,255,0.7)"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis stroke="rgba(255,255,255,0.7)" fontSize={12} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="links" fill="#06b6d4" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Clicks Chart */}
      <Card className="backdrop-blur-lg bg-black border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Clicks per Day</CardTitle>
          <CardDescription className="text-white/70">
            Total link clicks per day
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={clicksData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis 
                  dataKey="date" 
                  stroke="rgba(255,255,255,0.7)"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis stroke="rgba(255,255,255,0.7)" fontSize={12} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line 
                  type="monotone" 
                  dataKey="clicks" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Device Breakdown */}
      <Card className="backdrop-blur-lg bg-black border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Top Devices / Platforms</CardTitle>
          <CardDescription className="text-white/70">
            Breakdown by device type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ChartTooltip content={<ChartTooltipContent />} />
              </PieChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAnalytics;
