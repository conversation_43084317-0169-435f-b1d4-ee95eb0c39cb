
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>, Link, MousePointer, Crown, UserPlus, TrendingUp } from 'lucide-react';

interface AdminKPICardsProps {
  data: {
    totalUsers: number;
    totalLinks: number;
    totalClicks: number;
    proUsers: number;
    newUsers7d: number;
  };
}

const AdminKPICards: React.FC<AdminKPICardsProps> = ({ data }) => {
  const kpiCards = [
    {
      title: 'Total Users',
      value: data.totalUsers.toLocaleString(),
      icon: Users,
      description: 'Registered users',
      change: '+12.5%',
      trend: 'up'
    },
    {
      title: 'Total Links',
      value: data.totalLinks.toLocaleString(),
      icon: Link,
      description: 'Links created',
      change: '+8.2%',
      trend: 'up'
    },
    {
      title: 'Total Clicks',
      value: data.totalClicks.toLocaleString(),
      icon: MousePointer,
      description: 'Cumulative clicks',
      change: '+15.3%',
      trend: 'up'
    },
    {
      title: 'Pro Users',
      value: data.proUsers.toLocaleString(),
      icon: Crown,
      description: 'Premium subscribers',
      change: '+23.1%',
      trend: 'up'
    },
    {
      title: 'New Users (7d)',
      value: data.newUsers7d.toLocaleString(),
      icon: UserPlus,
      description: 'Last 7 days',
      change: '+5.7%',
      trend: 'up'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      {kpiCards.map((card, index) => (
        <Card key={index} className="backdrop-blur-lg bg-black border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white/90">
              {card.title}
            </CardTitle>
            <card.icon className="h-4 w-4 text-white/70" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{card.value}</div>
            <div className="flex items-center space-x-2 text-xs text-white/70">
              <span>{card.description}</span>
              <div className="flex items-center text-green-400">
                <TrendingUp className="h-3 w-3 mr-1" />
                {card.change}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default AdminKPICards;
