
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  Ban, 
  Star, 
  ExternalLink,
  TrendingUp,
  Calendar
} from 'lucide-react';

const AdminLinkAnalytics = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('clicks');

  // Mock link data
  const [links, setLinks] = useState([
    {
      id: '1',
      shortLink: 'yourdomain.com/abc123',
      originalUrl: 'https://instagram.com/johndoe',
      totalClicks: 15420,
      owner: 'johndoe',
      createdAt: '2024-01-15',
      status: 'Active',
      featured: true
    },
    {
      id: '2',
      shortLink: 'yourdomain.com/xyz789',
      originalUrl: 'https://youtube.com/channel/janesmithdesign',
      totalClicks: 8934,
      owner: 'janesmithdesign',
      createdAt: '2024-01-10',
      status: 'Active',
      featured: false
    },
    {
      id: '3',
      shortLink: 'yourdomain.com/def456',
      originalUrl: 'https://linkedin.com/in/mikebrown',
      totalClicks: 7251,
      owner: 'mikebrown',
      createdAt: '2024-01-05',
      status: 'Deactivated',
      featured: false
    },
    {
      id: '4',
      shortLink: 'yourdomain.com/ghi789',
      originalUrl: 'https://twitter.com/sarahwilson',
      totalClicks: 12678,
      owner: 'sarahwilson',
      createdAt: '2023-12-20',
      status: 'Active',
      featured: true
    },
    {
      id: '5',
      shortLink: 'yourdomain.com/jkl012',
      originalUrl: 'https://tiktok.com/@alexchen',
      totalClicks: 23456,
      owner: 'alexchen',
      createdAt: '2023-12-15',
      status: 'Active',
      featured: false
    }
  ]);

  const filteredLinks = links
    .filter(link =>
      link.shortLink.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.originalUrl.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === 'clicks') return b.totalClicks - a.totalClicks;
      if (sortBy === 'date') return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      return 0;
    });

  const handleLinkAction = (linkId: string, action: string) => {
    setLinks(links.map(link => {
      if (link.id === linkId) {
        switch (action) {
          case 'deactivate':
            return { ...link, status: 'Deactivated' };
          case 'activate':
            return { ...link, status: 'Active' };
          case 'feature':
            return { ...link, featured: true };
          case 'unfeature':
            return { ...link, featured: false };
          default:
            return link;
        }
      }
      return link;
    }));
  };

  const totalClicks = links.reduce((sum, link) => sum + link.totalClicks, 0);
  const activeLinks = links.filter(link => link.status === 'Active').length;
  const featuredLinks = links.filter(link => link.featured).length;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="backdrop-blur-lg bg-white/10 border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-white/70">Total Clicks</p>
                <p className="text-2xl font-bold text-white">{totalClicks.toLocaleString()}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-white/70" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="backdrop-blur-lg bg-white/10 border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-white/70">Active Links</p>
                <p className="text-2xl font-bold text-white">{activeLinks}</p>
              </div>
              <ExternalLink className="h-8 w-8 text-white/70" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="backdrop-blur-lg bg-white/10 border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-white/70">Featured Links</p>
                <p className="text-2xl font-bold text-white">{featuredLinks}</p>
              </div>
              <Star className="h-8 w-8 text-white/70" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Links Table */}
      <Card className="backdrop-blur-lg bg-white/10 border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white">Top Performing Links</CardTitle>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder="Search links..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                />
              </div>
              <Button 
                variant="outline" 
                className="border-white/20 text-white hover:bg-white/10"
                onClick={() => setSortBy(sortBy === 'clicks' ? 'date' : 'clicks')}
              >
                Sort by {sortBy === 'clicks' ? 'Date' : 'Clicks'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border border-white/20">
            <Table>
              <TableHeader>
                <TableRow className="border-white/20">
                  <TableHead className="text-white/90">Short Link</TableHead>
                  <TableHead className="text-white/90">Original URL</TableHead>
                  <TableHead className="text-white/90">Total Clicks</TableHead>
                  <TableHead className="text-white/90">Owner</TableHead>
                  <TableHead className="text-white/90">Status</TableHead>
                  <TableHead className="text-white/90">Created At</TableHead>
                  <TableHead className="text-white/90">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLinks.map((link) => (
                  <TableRow key={link.id} className="border-white/20">
                    <TableCell className="text-white font-medium">
                      <div className="flex items-center space-x-2">
                        {link.featured && <Star className="h-4 w-4 text-yellow-400 fill-current" />}
                        <span className="font-mono text-sm">{link.shortLink}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-white/80 max-w-xs truncate">
                      {link.originalUrl}
                    </TableCell>
                    <TableCell className="text-white font-semibold">
                      {link.totalClicks.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-white/80">
                      {link.owner}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={link.status === 'Active' ? 'default' : 'secondary'}
                        className={link.status === 'Active' 
                          ? 'bg-green-500/20 text-green-300 border-green-500/30' 
                          : 'bg-red-500/20 text-red-300 border-red-500/30'
                        }
                      >
                        {link.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-white/80">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{link.createdAt}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0 text-white/70 hover:text-white">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
                          <DropdownMenuItem 
                            onClick={() => window.open(link.originalUrl, '_blank')}
                            className="text-white hover:bg-gray-700"
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Link
                          </DropdownMenuItem>
                          {link.status === 'Active' ? (
                            <DropdownMenuItem 
                              onClick={() => handleLinkAction(link.id, 'deactivate')}
                              className="text-white hover:bg-gray-700"
                            >
                              <Ban className="mr-2 h-4 w-4" />
                              Deactivate
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              onClick={() => handleLinkAction(link.id, 'activate')}
                              className="text-white hover:bg-gray-700"
                            >
                              <ExternalLink className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )}
                          {link.featured ? (
                            <DropdownMenuItem 
                              onClick={() => handleLinkAction(link.id, 'unfeature')}
                              className="text-white hover:bg-gray-700"
                            >
                              <Star className="mr-2 h-4 w-4" />
                              Remove Feature
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              onClick={() => handleLinkAction(link.id, 'feature')}
                              className="text-white hover:bg-gray-700"
                            >
                              <Star className="mr-2 h-4 w-4" />
                              Feature Link
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminLinkAnalytics;
