
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { 
  Bell, 
  Crown, 
  Download, 
  Settings, 
  Shield, 
  Activity,
  AlertTriangle,
  Mail
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

const AdminSystemSettings = () => {
  const { toast } = useToast();
  const [broadcastMessage, setBroadcastMessage] = useState('');
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [proPrice, setProPrice] = useState('9.99');
  const [proFeatures, setProFeatures] = useState([
    'Unlimited links',
    'Custom themes',
    'Analytics dashboard',
    'Custom domain',
    'Remove branding'
  ]);

  const handleBroadcastMessage = () => {
    if (!broadcastMessage.trim()) {
      toast({
        title: "Error",
        description: "Please enter a message to broadcast",
        variant: "destructive"
      });
      return;
    }
    
    // Here you would send the broadcast message to all users
    toast({
      title: "Success",
      description: "Broadcast message sent to all users",
    });
    setBroadcastMessage('');
  };

  const handleExportUsers = () => {
    // Here you would generate and download the CSV file
    toast({
      title: "Success",
      description: "User list exported successfully",
    });
  };

  const handleToggleMaintenance = () => {
    setMaintenanceMode(!maintenanceMode);
    toast({
      title: maintenanceMode ? "Maintenance Mode Disabled" : "Maintenance Mode Enabled",
      description: maintenanceMode 
        ? "Site is now accessible to all users" 
        : "Site is now in maintenance mode",
    });
  };

  const handleUpdateProPlan = () => {
    toast({
      title: "Success",
      description: "Pro plan settings updated successfully",
    });
  };

  const adminLogs = [
    {
      id: 1,
      action: 'User suspended',
      admin: '<EMAIL>',
      target: 'mikebrown',
      timestamp: '2024-01-20 14:30:00'
    },
    {
      id: 2,
      action: 'Broadcast message sent',
      admin: '<EMAIL>',
      target: 'All users',
      timestamp: '2024-01-20 12:15:00'
    },
    {
      id: 3,
      action: 'User promoted to Pro',
      admin: '<EMAIL>',
      target: 'johndoe',
      timestamp: '2024-01-20 10:45:00'
    },
    {
      id: 4,
      action: 'Link deactivated',
      admin: '<EMAIL>',
      target: 'Link #123456',
      timestamp: '2024-01-19 16:20:00'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Broadcast Message */}
      <Card className="backdrop-blur-lg bg-white/10 border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Bell className="mr-2 h-5 w-5" />
            Broadcast Message
          </CardTitle>
          <CardDescription className="text-white/70">
            Send notifications to all users
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter your message for all users..."
            value={broadcastMessage}
            onChange={(e) => setBroadcastMessage(e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
            rows={4}
          />
          <Button 
            onClick={handleBroadcastMessage}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Mail className="mr-2 h-4 w-4" />
            Send Broadcast
          </Button>
        </CardContent>
      </Card>

      {/* Pro Plan Management */}
      <Card className="backdrop-blur-lg bg-white/10 border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Crown className="mr-2 h-5 w-5" />
            Pro Plan Management
          </CardTitle>
          <CardDescription className="text-white/70">
            Adjust Pro features and pricing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pro-price" className="text-white">Monthly Price ($)</Label>
              <Input
                id="pro-price"
                type="number"
                value={proPrice}
                onChange={(e) => setProPrice(e.target.value)}
                className="bg-white/10 border-white/20 text-white"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-white">Pro Features</Label>
              <div className="space-y-2">
                {proFeatures.map((feature, index) => (
                  <div key={index} className="text-white/80 text-sm">
                    • {feature}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <Button 
            onClick={handleUpdateProPlan}
            className="bg-yellow-600 hover:bg-yellow-700"
          >
            Update Pro Plan
          </Button>
        </CardContent>
      </Card>

      {/* System Tools */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Export & Maintenance */}
        <Card className="backdrop-blur-lg bg-white/10 border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              System Tools
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleExportUsers}
              variant="outline"
              className="w-full border-white/20 text-white hover:bg-white/10"
            >
              <Download className="mr-2 h-4 w-4" />
              Export User List (CSV)
            </Button>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-white">Maintenance Mode</Label>
                <p className="text-sm text-white/70">
                  Temporarily disable site access
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Switch
                    checked={maintenanceMode}
                    className="data-[state=checked]:bg-red-600"
                  />
                </AlertDialogTrigger>
                <AlertDialogContent className="bg-gray-800 border-gray-700">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="text-white flex items-center">
                      <AlertTriangle className="mr-2 h-5 w-5 text-red-400" />
                      {maintenanceMode ? 'Disable' : 'Enable'} Maintenance Mode
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-gray-300">
                      {maintenanceMode 
                        ? 'This will make the site accessible to all users again.'
                        : 'This will make the site inaccessible to regular users. Only administrators will be able to access the site.'
                      }
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel className="bg-gray-700 text-white border-gray-600">
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleToggleMaintenance}
                      className={maintenanceMode ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
                    >
                      {maintenanceMode ? 'Disable' : 'Enable'}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardContent>
        </Card>

        {/* Admin Activity Logs */}
        <Card className="backdrop-blur-lg bg-white/10 border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Admin Activity Logs
            </CardTitle>
            <CardDescription className="text-white/70">
              Recent administrative actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {adminLogs.map((log) => (
                <div key={log.id} className="border-b border-white/10 pb-2 last:border-b-0">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-white text-sm font-medium">{log.action}</p>
                      <p className="text-white/70 text-xs">
                        Target: {log.target} • By: {log.admin}
                      </p>
                    </div>
                    <p className="text-white/50 text-xs">{log.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminSystemSettings;
