
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  UserX, 
  User<PERSON>heck, 
  Trash2, 
  Crown,
  Shield
} from 'lucide-react';

const AdminUserManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<any>(null);

  // Mock user data
  const [users, setUsers] = useState([
    {
      id: '1',
      username: 'johndoe',
      email: '<EMAIL>',
      accountType: 'Pro',
      status: 'Active',
      totalLinks: 15,
      registrationDate: '2024-01-15',
      lastLogin: '2024-01-20'
    },
    {
      id: '2',
      username: 'janesmithdesign',
      email: '<EMAIL>',
      accountType: 'Free',
      status: 'Active',
      totalLinks: 8,
      registrationDate: '2024-01-10',
      lastLogin: '2024-01-19'
    },
    {
      id: '3',
      username: 'mikebrown',
      email: '<EMAIL>',
      accountType: 'Free',
      status: 'Suspended',
      totalLinks: 3,
      registrationDate: '2024-01-05',
      lastLogin: '2024-01-12'
    },
    {
      id: '4',
      username: 'sarahwilson',
      email: '<EMAIL>',
      accountType: 'Pro',
      status: 'Active',
      totalLinks: 22,
      registrationDate: '2023-12-20',
      lastLogin: '2024-01-20'
    }
  ]);

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUserAction = (userId: string, action: string) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        switch (action) {
          case 'suspend':
            return { ...user, status: 'Suspended' };
          case 'activate':
            return { ...user, status: 'Active' };
          case 'promote':
            return { ...user, accountType: 'Pro' };
          case 'demote':
            return { ...user, accountType: 'Free' };
          default:
            return user;
        }
      }
      return user;
    }));
  };

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId));
  };

  return (
    <Card className="backdrop-blur-lg bg-black border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white">User Management</CardTitle>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50"
              />
            </div>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10 hover:text-white">
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border border-white/20">
          <Table>
            <TableHeader>
              <TableRow className="border-white/20">
                <TableHead className="text-white/90">Username</TableHead>
                <TableHead className="text-white/90">Email</TableHead>
                <TableHead className="text-white/90">Account Type</TableHead>
                <TableHead className="text-white/90">Status</TableHead>
                <TableHead className="text-white/90">Total Links</TableHead>
                <TableHead className="text-white/90">Last Login</TableHead>
                <TableHead className="text-white/90">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id} className="border-white/20">
                  <TableCell className="text-white font-medium">
                    {user.username}
                  </TableCell>
                  <TableCell className="text-white/80">
                    {user.email}
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={user.accountType === 'Pro' ? 'default' : 'secondary'}
                      className={user.accountType === 'Pro' 
                        ? 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30' 
                        : 'bg-gray-500/20 text-gray-300 border-gray-500/30'
                      }
                    >
                      {user.accountType === 'Pro' && <Crown className="w-3 h-3 mr-1" />}
                      {user.accountType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={user.status === 'Active' ? 'default' : 'destructive'}
                      className={user.status === 'Active' 
                        ? 'bg-green-500/20 text-green-300 border-green-500/30' 
                        : 'bg-red-500/20 text-red-300 border-red-500/30'
                      }
                    >
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-white/80">
                    {user.totalLinks}
                  </TableCell>
                  <TableCell className="text-white/80">
                    {user.lastLogin}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0 text-white/70 hover:text-white">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
                        <DropdownMenuItem 
                          onClick={() => setSelectedUser(user)}
                          className="text-white hover:bg-gray-700"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Profile
                        </DropdownMenuItem>
                        {user.status === 'Active' ? (
                          <DropdownMenuItem 
                            onClick={() => handleUserAction(user.id, 'suspend')}
                            className="text-white hover:bg-gray-700"
                          >
                            <UserX className="mr-2 h-4 w-4" />
                            Suspend
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem 
                            onClick={() => handleUserAction(user.id, 'activate')}
                            className="text-white hover:bg-gray-700"
                          >
                            <UserCheck className="mr-2 h-4 w-4" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        {user.accountType === 'Free' ? (
                          <DropdownMenuItem 
                            onClick={() => handleUserAction(user.id, 'promote')}
                            className="text-white hover:bg-gray-700"
                          >
                            <Crown className="mr-2 h-4 w-4" />
                            Promote to Pro
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem 
                            onClick={() => handleUserAction(user.id, 'demote')}
                            className="text-white hover:bg-gray-700"
                          >
                            <Shield className="mr-2 h-4 w-4" />
                            Demote to Free
                          </DropdownMenuItem>
                        )}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem 
                              onSelect={(e) => e.preventDefault()}
                              className="text-red-400 hover:bg-red-900/20"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete User
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="bg-gray-800 border-gray-700">
                            <AlertDialogHeader>
                              <AlertDialogTitle className="text-white">
                                Delete User
                              </AlertDialogTitle>
                              <AlertDialogDescription className="text-gray-300">
                                Are you sure you want to delete this user? This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="bg-gray-700 text-white border-gray-600">
                                Cancel
                              </AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => handleDeleteUser(user.id)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminUserManagement;
