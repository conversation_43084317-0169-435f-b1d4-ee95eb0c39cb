import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import axios from 'axios';

interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  bio: string;
  profileImage: string;
  file: File | null;
  phone: string;
  location: string;
  professionalTitle: string;
}

interface Link {
  id: string;
  label: string;
  url: string;
  icon: string;
  order: number;
}

interface AuthContextType {
  user: User | null;
  links: Link[];
  login: (email: string, password: string) => Promise<{status: boolean, message:string, user:User}>;
  signup: (email: string, password: string, username: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: FormData) => Promise<void>;
  addLink: (link: Omit<Link, 'id' | 'order'>) => Promise<void>;
  updateLink: (id: string, data: Partial<Link>) => Promise<void>;
  deleteLink: (id: string) => Promise<void>;
  reorderLinks: (links: Link[]) => Promise<void>;
  checkUser: (username: string) => Promise<{ status: boolean; message: string }>;
  checkEmail: (email: string) => Promise<{ status: boolean; message: string }>;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const API_URL = import.meta.env.VITE_API_URL || `http://localhost:3000/api`;
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true  
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [links, setLinks] = useState<Link[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Memoize logout to prevent re-creating it on every render
  // and to use it as a stable dependency in useEffect.
  const logout = useCallback(() => {
   // Cookie.remove('token');
    api.get('/logout');
    setUser(null);
    setLinks([]);
  }, []);

  // Effect for setting up interceptors
  useEffect(() => {
    // Add a request interceptor to inject the auth token
    const requestInterceptor = api.interceptors.request.use(config => {
      return config;
    });
    // Add a response interceptor to handle 401 Unauthorized errors globally
    const responseInterceptor = api.interceptors.response.use(
      response => response,
      error => {
        if (error.response && error.response.status === 401) {
          logout(); // Token is invalid or expired, log the user out
        }
        return Promise.reject(error);
      }
    );
    // Cleanup function to eject interceptors when the component unmounts
    return () => {
      api.interceptors.request.eject(requestInterceptor);
      api.interceptors.response.eject(responseInterceptor);
    };
  }, [logout]);

  // Effect for initial user load
  useEffect(() => {
    setIsLoading(true);
    api.get('/admin/profile')
      .then(response => {
        const { user, links } = response.data;
        setUser(user);
        setLinks(links || []);
      })
      .catch((error) => {
        console.error('Error fetching user profile:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []); // Run only once on mount

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await api.post('/login',{ email, password });
      
      if (!response.data.status){
        throw new Error(response.data.message);
      }

      const { user, links } = response.data;
      setUser(user);
      setLinks(links || []);
      return {status: true, message: 'Login successful', user};

    } catch (error) {
      console.error('Login error:', error); 
      return {status: false, message: error.message, user: null};     
    } finally {
      setIsLoading(false);
    }
  };

  const checkUser = async (username: string) => {    
    try {      
      const response = await api.get(`/checkuser/${username}`);
      console.log('Check user response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Check user error:', error);
      throw error;
    }
  };

  const checkEmail = async (email: string) => { 
    try {      
      const response = await api.get(`/checkemail/${email}`);
      console.log('Check email response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Check email error:', error);
      throw error;
    }
  };

  const signup = async (email: string, password: string, username: string) => {
    setIsLoading(true);
    try {
      const response = await api.post('/register',
        {
          email,
          password,
          username
        },
        {
          withCredentials: true
        }
      );

      if(response.data.status === false){
        return{
          status: false,
          message: response?.data.message
        };
      }
      
      const { user } = response.data;
      setUser(user);
      setLinks([]);
      return user;

    } catch (error) {      
      return {
        status: false,
        message: error
      };
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: FormData) => {
    try {
      
      console.log('Updating profile API:', API_URL);   

      const response = await api.put('/admin/profile', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });
      const updatedUser = { ...user, ...response.data.user };
      setUser(updatedUser);
      return updatedUser;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  };

  const addLink = async (linkData: Omit<Link, 'id' | 'order'>) => {
    try {
      const response = await api.post('/admin/links', linkData);
      const newLink = response.data.link;
      const updatedLinks = [...links, newLink];
      setLinks(updatedLinks);
      return newLink;
    } catch (error) {

      console.log('Error adding link:', error);
      console.error('Add link error:', error);
      throw error;
    }
  };

  // Other methods would be updated to use the API as well
  // For brevity, I'm showing just a few examples

  const updateLink = async (id: string, data: Partial<Link>) => {
    try {
      const response = await api.put(`/admin/links/${id}`, data);
      const updatedLinks = links.map(link =>
        link.id === id ? { ...link, ...response.data.link } : link
      );
      setLinks(updatedLinks);
      return response.data.link;
    } catch (error) {
      console.error('Update link error:', error);
      throw error;
    }
  };

  const deleteLink = async (id: string) => {
    try {
      await api.delete(`/admin/links/${id}`);
      const updatedLinks = links.filter(link => link.id !== id);
      setLinks(updatedLinks);
    } catch (error) {
      console.error('Delete link error:', error);
      throw error;
    }
  };

  const reorderLinks = async (reorderedLinks: Link[]) => {
    try {
      const response = await api.put('/admin/links/reorder', reorderedLinks);
      const updatedLinks = response.data.links;
      setLinks(updatedLinks);
      return updatedLinks;
    } catch (error) {
      console.error('Reorder links error:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      links,
      login,
      signup,
      logout,
      updateProfile,
      addLink,
      updateLink,
      deleteLink,
      reorderLinks,
      isLoading,
      checkUser,
      checkEmail
    }}>
      {children}
    </AuthContext.Provider>
  );
};
