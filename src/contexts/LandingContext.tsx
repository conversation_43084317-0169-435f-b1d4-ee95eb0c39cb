import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

export interface LandingProfile{
   userID: string;
   name: string;
   title: string;
   bio: string;
   about: string;
   journey: string;
   email: string;
   phone: string;
   location: string;
   github: string;
   linkedin: string;
   twitter: string;
   image: string;
   file: File | null;   
}

interface LandingContextType {
   pageData: LandingProfile;
   setPageData: React.Dispatch<React.SetStateAction<LandingProfile>>;
   updateProfile: (data: FormData) => Promise<void>;
}

const API_URL = import.meta.env.VITE_API_URL || `http://localhost:3000/api`;
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true  
});

export const LandingContext = createContext<LandingContextType>(null);
export function LandingProvider({ children }: { children: React.ReactNode }) {

   const [pageData, setPageData] = useState<LandingProfile>(
      {
         userID: '',
         name: '',
         title: '',
         bio: '',
         about: '',
         journey: '',
         email: '',
         phone: '',
         location: '',
         github: '',
         linkedin: '',
         twitter: '',
         image: '',
         file: null
      }
   );
   
   const getPageData = async () => {
      try {      
         const result = await api.get(`/admin/landing/profile`);           
         const data = result.data;
         if (data.status){
            const profile : LandingProfile = {
               userID: data.profile.userID,
               name: data.profile.name,
               title: data.profile.title,
               bio: data.profile.bio,
               about: data.profile.about,
               journey: data.profile.journey,
               email: data.profile.email,
               phone: data.profile.phone,
               location: data.profile.location,
               github: data.profile.github,
               linkedin: data.profile.linkedin,
               twitter: data.profile.twitter,
               image: data.profile.image,
               file: null
            }
            setPageData(profile);
         }        
      } catch (error) {
         console.error('Get profile error:', error);        
      }
   };

   useEffect(() => {
      getPageData();
   }, []);

   const updateProfile = async (data: FormData) => {
      try {
         const response = await api.put('/admin/landing/profile', data);
         const result = response.data.profile;      
         return result;
      } catch (error) {
         console.error('Update profile error:', error);
         throw error;
      }
   };   

   return (
      <LandingContext.Provider 
         value={{ 
            pageData, 
            setPageData,
            updateProfile 
         }}>
         {children}
      </LandingContext.Provider>
   );

}