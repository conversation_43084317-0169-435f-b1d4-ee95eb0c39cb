import axios from 'axios';

export interface LandingProfile{
   userID: string;
   name: string;
   title: string;
   bio: string;
   email: string;
   phone: string;
   location: string;
   github: string;
   linkedin: string;
   twitter: string;
   image: string;
   file: File | null;   
}

const API_URL = import.meta.env.VITE_API_URL || `http://localhost:3000/api`;
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true  
});

export default async function GetProfile() : Promise<{status: boolean, message:string, profile:LandingProfile | null}> {
     
   try {      
      const result = await api.get(`/admin/landing/profile`);           
      const data = result.data;   
      //console.log('Get profile data : ', data.profile);

      if (!data.status){
         return {
            status: false,
            message: data.message,
            profile: null
         };
      }
      const profile : LandingProfile = {
         userID: data.profile.userID,
         name: data.profile.name,
         title: data.profile.title,
         bio: data.profile.bio,
         email: data.profile.email,
         phone: data.profile.phone,
         location: data.profile.location,
         github: data.profile.github,
         linkedin: data.profile.linkedin,
         twitter: data.profile.twitter,
         image: data.profile.image,
         file: null
      }
      return {
         status: true,
         message: 'Profile fetched successfully',      
         profile
      };           
   } catch (error) {
      console.error('Get profile error:', error);
      return {
         status: false,
         message: 'Error fetching profile',
         profile: null
      };
   }   
   
};

export const updateProfile = async (data: FormData) => {
   try {
      const response = await api.post('/admin/landing/profile', data);
      const result = response.data.profile;      
      return result;
   } catch (error) {
      console.error('Update profile error:', error);
      throw error;
   }
};
