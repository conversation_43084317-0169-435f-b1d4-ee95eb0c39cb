
import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, MapPin, Edit2, Trash2, GripVertical, ExternalLink, Mail, Instagram, Youtube, Linkedin, Github, Twitter, Facebook, Dribbble } from 'lucide-react';
import axios from 'axios';
const API_URL = import.meta.env.VITE_API_URL || `http://localhost:3000/api`;
const IMG_URL = import.meta.env.VITE_IMG_URL || `http://localhost:3030/uploads`;
const api = axios.create({
  baseURL: API_URL
});

interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  professionalTitle?: string;
  bio: string;
  location?: string;
  profileImage: string;
  availabilityStatus?: 'available' | 'busy' | 'unavailable';
}

interface Link {
  id: string;
  label: string;
  url: string;
  icon: string;
  order: number;
  isVisible: boolean;
}

interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  projectUrl?: string;
  technologies: string[];
  order: number;
}

interface Testimonial {
  id: string;
  clientName: string;
  clientImage?: string;
  testimonialText: string;
  order: number;
}

const iconMap = {
  'external-link': ExternalLink,
  'mail': Mail,
  'instagram': Instagram,
  'youtube': Youtube,
  'linkedin': Linkedin,
  'github': Github,
  'facebook': Facebook,
  'dribbble': Dribbble,
  'twitter': Twitter
};

const statusConfig = {
  available: { label: 'Available for work', color: 'bg-green-500' },
  busy: { label: 'Busy - Limited availability', color: 'bg-yellow-500' },
  unavailable: { label: 'Not accepting new clients', color: 'bg-red-500' }
};

export default function PageContext({ username }: { username: string }) {
  const [user, setUser] = useState<User | null>(null);
  const [links, setLinks] = useState<Link[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    const getPageUser = async () => {
      try {
        const response = await api.get(`/pages/${username}`);
        setUser(response.data.user);
        setLinks(response.data.links || []);
      } catch (error) {
        console.error('Error fetching user Page:', error);
      }
      finally {
        setIsLoading(false);
      }
    }
    getPageUser();
  }, []);

  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const getIcon = (iconName: string) => {
    return iconMap[iconName as keyof typeof iconMap] || ExternalLink;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-4xl font-bold mb-4">Loading...</h1>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-4xl font-bold mb-4">Profile not found</h1>
          <p className="text-white/80">The requested profile does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Profile Section */}        
        <div className="text-center mb-12">          
          {user.profileImage && (
            <Avatar className="w-32 h-32 mx-auto mb-6 ring-4 ring-white/20">
              <AvatarImage src={`${IMG_URL}/${user.profileImage}`} alt={user.displayName} />
              <AvatarFallback className="text-3xl bg-white/20 text-white">
                {user.displayName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          )}
          <h1 className="text-4xl font-bold text-white mb-2">
            {user.displayName}
          </h1>

          {user.professionalTitle && (
            <p className="text-xl text-white/90 mb-4 font-medium">
              {user.professionalTitle}
            </p>
          )}

          {user.location && (
            <div className="flex items-center justify-center text-white/80 mb-4">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{user.location}</span>
            </div>
          )}

          {user.availabilityStatus && (
            <Badge className={`${statusConfig[user.availabilityStatus].color} text-white mb-6`}>
              {statusConfig[user.availabilityStatus].label}
            </Badge>
          )}

          {user.bio && (
            <p className="text-white/80 text-lg leading-relaxed max-w-2xl mx-auto">
              {user.bio}
            </p>
          )}
        </div>

        {/* Contact Links Section */}
        <div className="mb-8">
          <div className="space-y-4 max-w-md mx-auto">
            {links.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-white/60">No contact links available yet.</p>
              </div>
            ) : (
              links
                .sort((a, b) => a.order - b.order)
                .map((link) => {
                  const IconComponent = getIcon(link.icon);
                  return (
                    <Button
                      key={link.id}
                      onClick={() => handleLinkClick(link.url)}
                      className="w-full h-14 bg-white/10 backdrop-blur-lg border border-white/20 text-white hover:bg-white/20 hover:scale-105 transition-all duration-200 flex items-center justify-center space-x-3 group"
                      variant="outline"
                    >
                      <IconComponent className="w-5 h-5 group-hover:scale-110 transition-transform" />
                      <span className="font-medium text-lg">{link.label}</span>
                      <ExternalLink className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity" />
                    </Button>
                  );
                })
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-white/40 text-sm">
            Create your own professional link page at{' '}
            <a href="http://localhost:5173" className="text-white underline">
              linkhub.com
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
