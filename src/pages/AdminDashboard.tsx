
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>, 
  Link, 
  MousePointer, 
  Crown, 
  UserPlus, 
  Settings,
  Download,
  Bell,
  Shield,
  Activity
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import AdminKPICards from '@/components/admin/AdminKPICards';
import AdminAnalytics from '@/components/admin/AdminAnalytics';
import AdminUserManagement from '@/components/admin/AdminUserManagement';
import AdminLinkAnalytics from '@/components/admin/AdminLinkAnalytics';
import AdminSystemSettings from '@/components/admin/AdminSystemSettings';

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - in real app, this would come from API
  const [dashboardData, setDashboardData] = useState({
    totalUsers: 2547,
    totalLinks: 18392,
    totalClicks: 847293,
    proUsers: 127,
    newUsers7d: 89,
    userGrowth: [
      { date: '2024-01-01', users: 2458 },
      { date: '2024-01-02', users: 2467 },
      { date: '2024-01-03', users: 2489 },
      { date: '2024-01-04', users: 2501 },
      { date: '2024-01-05', users: 2523 },
      { date: '2024-01-06', users: 2538 },
      { date: '2024-01-07', users: 2547 }
    ]
  });

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Activity },
    { id: 'users', label: 'User Management', icon: Users },
    { id: 'links', label: 'Link Analytics', icon: Link },
    { id: 'settings', label: 'System Settings', icon: Settings }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
            <p className="text-white/80">Welcome back, {user?.displayName}!</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="border-white/20 text-white">
              <Shield className="w-4 h-4 mr-2" />
              Administrator
            </Badge>
            <Button 
              variant="outline" 
              className="border-white/20 text-white hover:bg-white/10"
              onClick={logout}
            >
              Logout
            </Button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex gap-4 mb-8 overflow-x-auto">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'outline'}
              className={activeTab === tab.id 
                ? 'bg-white text-purple-600 whitespace-nowrap' 
                : 'border-white/20 text-white hover:bg-white/10 whitespace-nowrap'
              }
              onClick={() => setActiveTab(tab.id)}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </Button>
          ))}
        </div>

        {/* Content */}
        <div className="space-y-8">
          {activeTab === 'overview' && (
            <>
              <AdminKPICards data={dashboardData} />
              <AdminAnalytics data={dashboardData} />
            </>
          )}
          {activeTab === 'users' && <AdminUserManagement />}
          {activeTab === 'links' && <AdminLinkAnalytics />}
          {activeTab === 'settings' && <AdminSystemSettings />}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
