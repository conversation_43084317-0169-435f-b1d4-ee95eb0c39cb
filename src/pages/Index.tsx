
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AuthForm from '@/components/AuthForm';
import Dashboard from '@/components/Dashboard';
import { Button } from "@/components/ui/button";
import { Link, Star, Users, Zap } from 'lucide-react';
import '../index.css';

const Index = () => {
  const { user, isLoading } = useAuth();
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login');  
  const handleSignIn = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: 'smooth'
    });    
    setAuthMode('login');
  };
  const handleSignUp = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: 'smooth'
    });    
    setAuthMode('signup');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (user) {
    return <Dashboard />;
  }

  return (
     <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-white backdrop-blur-lg rounded-full p-4">                         
              <img src="./images/logoLinkHub.png" alt="Logo" className="w-48 h-auto" />
            </div>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Your Personal
            <br />
            <span className="bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
              Drop u link
            </span>
          </h1>
          
          <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto leading-relaxed text-center">
            Just drop your link. <br></br>
            Create a beautiful landing page that brings all your important links together. Perfect for social media bios, business cards, and sharing your digital presence.
          </p>

          {/* Features */}
          <div className="grid md:grid-cols-3 gap-8 mb-16 max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <div className="bg-white/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Lightning Fast</h3>
              <p className="text-white/70">Set up your link page in minutes. No coding required.</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <div className="bg-white/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Easy Sharing</h3>
              <p className="text-white/70">Get a clean, memorable URL that's perfect for sharing.</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <div className="bg-white/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Beautiful Design</h3>
              <p className="text-white/70">Stunning templates that look great on any device.</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button 
              size="lg" 
              className="bg-white text-purple-700 hover:bg-white/90 font-semibold px-8 py-3 text-lg"
              onClick={handleSignUp}
            >
              Get Started Free
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-white/20 text-white hover:bg-white/90 font-semibold px-8 py-3 text-lg"
              onClick={handleSignIn}
            >
              Sign In
            </Button>
          </div>

          {/* Demo Link */}
          <div className="mb-16">
            <p className="text-white/60 mb-4">See it in action:</p>
            <Button
              variant="link"
              className="text-white underline text-lg"
              onClick={() => window.open('/johndoe', '_blank')}
            >
              View Demo Profile →
            </Button>
          </div>
        </div>

        {/* Auth Form */}
        <div className="max-w-md mx-auto">
          <AuthForm 
            mode={authMode} 
            onToggleMode={() => setAuthMode(authMode === 'login' ? 'signup' : 'login')} 
          />
        </div>
      </div>
    </div>
  );
};

export default Index;
