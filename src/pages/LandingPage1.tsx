// FILE: pages/landing.tsx

import React from 'react';

export default function LandingPage(): JSX.Element {
  return (
    <>
    

      <div className="min-h-screen bg-gradient-to-b from-slate-900 via-slate-900/80 to-slate-800 text-white">
        <header className="max-w-5xl mx-auto px-6 py-6 flex items-center justify-between">
          <a href="/">
            <a className="flex items-center gap-3">
              <svg width="36" height="36" viewBox="0 0 24 24" fill="none" className="text-white">
                <rect width="24" height="24" rx="6" fill="currentColor" className="opacity-10" />
                <path d="M6 17L12 7l6 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <span className="font-semibold text-lg">Your Name</span>
            </a>
          </a>

          <nav className="hidden md:flex items-center gap-6 text-sm text-white/80">
            <a href="#work" className="hover:text-white">Work</a>
            <a href="#services" className="hover:text-white">Services</a>
            <a href="#pricing" className="hover:text-white">Pricing</a>
            <a href="/auth">
              <a className="px-4 py-2 bg-white/10 rounded-md hover:bg-white/20">Sign up</a>
            </a>
          </nav>
        </header>

        <main className="max-w-5xl mx-auto px-6 py-12">
          <section className="grid lg:grid-cols-2 gap-10 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                Build a memorable personal brand that opens doors.
              </h1>
              <p className="mt-4 text-lg text-white/80">
                I help founders and creatives craft clear messaging, modern websites, and product-ready portfolios that convert visitors into opportunities.
              </p>

              <div className="mt-6 flex flex-wrap gap-3">
                <a href="/contact">
                  <a className="px-5 py-3 bg-indigo-600 hover:bg-indigo-500 rounded-md text-sm font-medium">Work with me</a>
                </a>
                <a href="/auth">
                  <a className="px-5 py-3 border border-white/20 rounded-md text-sm hover:bg-white/5">Get started — free</a>
                </a>
              </div>

              <div className="mt-8 flex flex-wrap gap-4 text-sm text-white/70">
                <div className="bg-white/5 px-3 py-2 rounded">
                  <strong className="text-white">8x</strong> more inbound leads on average
                </div>
                <div className="bg-white/5 px-3 py-2 rounded">
                  Trusted by founders & creatives
                </div>
              </div>
            </div>

            <div className="bg-white/5 rounded-lg p-6">
              <div className="h-60 bg-gradient-to-br from-white/5 to-white/3 rounded-md flex items-center justify-center">
                <div className="text-sm text-white/80 text-center">
                  <div className="font-medium">Showcase</div>
                  <div className="mt-2">A modern portfolio & case studies section</div>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-2 gap-3 text-xs text-white/80">
                <div className="bg-white/3 p-3 rounded">
                  <div className="font-semibold">Portfolio</div>
                  <div className="mt-1">Curated projects</div>
                </div>
                <div className="bg-white/3 p-3 rounded">
                  <div className="font-semibold">About</div>
                  <div className="mt-1">Clear positioning</div>
                </div>
                <div className="bg-white/3 p-3 rounded">
                  <div className="font-semibold">Contact</div>
                  <div className="mt-1">Inquiry form & calendar</div>
                </div>
                <div className="bg-white/3 p-3 rounded">
                  <div className="font-semibold">Blog</div>
                  <div className="mt-1">Thought leadership</div>
                </div>
              </div>
            </div>
          </section>

          <section id="services" className="mt-16">
            <h2 className="text-2xl font-semibold">Services</h2>
            <p className="mt-2 text-white/80">Core offerings to build and grow your brand.</p>

            <div className="mt-6 grid md:grid-cols-3 gap-4">
              {[
                { title: 'Brand Strategy', desc: 'Positioning, messaging, and audience definition.' },
                { title: 'Website & Portfolio', desc: 'Fast, responsive sites focused on conversion.' },
                { title: 'Content & Growth', desc: 'SEO, case studies and social content that resonates.' },
              ].map((s) => (
                <div key={s.title} className="bg-white/3 p-5 rounded-lg">
                  <div className="font-semibold">{s.title}</div>
                  <div className="mt-2 text-white/80 text-sm">{s.desc}</div>
                </div>
              ))}
            </div>
          </section>

          <section id="pricing" className="mt-16">
            <h2 className="text-2xl font-semibold">Pricing</h2>
            <p className="mt-2 text-white/80">Simple plans for freelancers and teams.</p>

            <div className="mt-6 grid md:grid-cols-3 gap-4">
              <PlanCard title="Starter" price="$0" desc="Personal site & portfolio" />
              <PlanCard title="Pro" price="$9/mo" desc="Everything in Starter + analytics" highlight />
              <PlanCard title="Agency" price="$49/mo" desc="Team-ready workflows" />
            </div>
          </section>

          <section id="contact" className="mt-16 bg-white/3 rounded-lg p-6">
            <h3 className="text-xl font-semibold">Ready to start?</h3>
            <p className="mt-2 text-white/80">Book a free consult or send a quick message.</p>
            <div className="mt-4 flex gap-3">
              <a href="/contact">
                <a className="px-4 py-2 bg-indigo-600 rounded-md">Book consult</a>
              </a>
              <a href="/auth">
                <a className="px-4 py-2 border border-white/20 rounded-md">Create account</a>
              </a>
            </div>
          </section>
        </main>

        <footer className="border-t border-white/6 mt-12">
          <div className="max-w-5xl mx-auto px-6 py-6 text-sm text-white/70 flex justify-between">
            <div>© {new Date().getFullYear()} Your Name</div>
            <div className="flex gap-4">
              <a href="#" className="hover:text-white">Privacy</a>
              <a href="#" className="hover:text-white">Terms</a>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

function PlanCard({ title, price, desc, highlight }: { title: string; price: string; desc: string; highlight?: boolean }) {
  return (
    <div className={`p-5 rounded-lg ${highlight ? 'bg-indigo-600/80' : 'bg-white/5'}`}>
      <div className="flex items-baseline justify-between">
        <div className="font-semibold">{title}</div>
        <div className="text-2xl font-bold">{price}</div>
      </div>
      <div className="mt-3 text-white/80">{desc}</div>
      <div className="mt-4">
        <button className={`w-full py-2 rounded-md ${highlight ? 'bg-white text-indigo-700' : 'bg-white/10 hover:bg-white/20'}`}>
          Choose
        </button>
      </div>
    </div>
  );
}