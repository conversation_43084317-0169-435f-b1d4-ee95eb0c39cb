// FILE: src/pages/Profile.tsx
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import ProjectManager, { Project } from "@/components/ProjectManager";

//type Project = { id: string; title: string; image: string; description?: string };
type User = {
  username: string;
  name: string;
  avatar?: string;
  bio?: string;
  skills: string[];
  projects: Project[];
  contact: { email?: string; website?: string; twitter?: string; linkedin?: string };
};

export default function Profile(): JSX.Element {
  const { username } = useParams<{ username: string }>();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [selected, setSelected] = useState<Project | null>(null);
  const [message, setMessage] = useState({ name: "", email: "", text: "" });
  const [sent, setSent] = useState(false);
  
  const [project, setProject] = useState<Project | null>(null);

  useEffect(() => {
    // Mock fetch - replace with real API call
    setLoading(true);
    const mock: User = {
      username: username || "jdoe",
      name: "John Doe",
      avatar: `https://avatars.dicebear.com/api/identicon/${username || "jdoe"}.svg`,
      bio: "Product designer & frontend dev. I build clean interfaces and case studies that convert.",
      skills: ["React", "TypeScript", "Design Systems", "Figma", "Next.js"],
      projects: [
        { id: "1", title: "Portfolio Redesign", image: "https://source.unsplash.com/collection/190727/800x600?sig=1", description: "Redesigned portfolio for a photographer." },
        { id: "2", title: "SaaS Dashboard", image: "https://source.unsplash.com/collection/190727/800x600?sig=2", description: "Analytics dashboard with realtime charts." },
        { id: "3", title: "E-commerce UI", image: "https://source.unsplash.com/collection/190727/800x600?sig=3", description: "Conversion-focused product pages." },
        { id: "4", title: "Landing Campaign", image: "https://source.unsplash.com/collection/190727/800x600?sig=4", description: "Marketing landing with A/B testing." },
      ],
      contact: { email: "<EMAIL>", website: "https://example.com", twitter: "https://twitter.com/example", linkedin: "https://linkedin.com/in/example" },
    };
    const t = setTimeout(() => {
      setUser(mock);
      setLoading(false);
    }, 300);
    return () => clearTimeout(t);
  }, [username]);

  function handleSend(e: React.FormEvent) {
    e.preventDefault();
    // Replace with API call
    console.log("Send message to", user?.username, message);
    setSent(true);
    setMessage({ name: "", email: "", text: "" });
    setTimeout(() => setSent(false), 3000);
  }

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center text-white">Loading profile...</div>;
  }

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center text-white">User not found</div>;
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white px-4 py-10">
      <div className="max-w-5xl mx-auto space-y-8">
        {/* Header */}
        <header className="flex flex-col md:flex-row items-center gap-6 bg-white/5 p-6 rounded-lg">
          <img src={user.avatar} alt={user.name} className="w-28 h-28 rounded-full border-2 border-white/10 object-cover" />
          <div className="flex-1">
            <h1 className="text-2xl font-bold">{user.name}</h1>
            <div className="text-sm text-white/80">@{user.username}</div>
            <p className="mt-3 text-white/80">{user.bio}</p>

            <div className="mt-4 flex flex-wrap gap-2">
              {user.skills.map((s) => (
                <span key={s} className="text-xs px-3 py-1 bg-white/5 rounded-full">
                  {s}
                </span>
              ))}
            </div>
          </div>

          <div className="w-full md:w-auto flex flex-col gap-3">
            <a href={`mailto:${user.contact.email}`} className="px-4 py-2 bg-indigo-600 rounded-md text-center">Email</a>
            {user.contact.website && (
              <a href={user.contact.website} target="_blank" rel="noreferrer" className="px-4 py-2 border border-white/10 rounded-md text-center">Website</a>
            )}
          </div>
        </header>        

        {/* Projects gallery */}
        <ProjectManager
         projects={user.projects}
         onChange={(projects) => setUser({ ...user, projects })}
        />

        {/* Contact & message */}
        <section className="grid md:grid-cols-2 gap-6 items-start">
          <div className="bg-white/5 p-6 rounded-lg">
            <h3 className="text-lg font-semibold">Contact</h3>
            <p className="mt-2 text-white/80">ช่องทางการติดต่อเพิ่มเติม</p>

            <ul className="mt-4 space-y-2 text-sm">
              {user.contact.email && (
                <li>
                  <strong>Email:</strong>{" "}
                  <a href={`mailto:${user.contact.email}`} className="text-indigo-400">{user.contact.email}</a>
                </li>
              )}
              {user.contact.website && (
                <li>
                  <strong>Website:</strong>{" "}
                  <a href={user.contact.website} target="_blank" rel="noreferrer" className="text-indigo-400">{user.contact.website}</a>
                </li>
              )}
              {user.contact.twitter && (
                <li>
                  <strong>Twitter:</strong>{" "}
                  <a href={user.contact.twitter} target="_blank" rel="noreferrer" className="text-indigo-400">Profile</a>
                </li>
              )}
              {user.contact.linkedin && (
                <li>
                  <strong>LinkedIn:</strong>{" "}
                  <a href={user.contact.linkedin} target="_blank" rel="noreferrer" className="text-indigo-400">Profile</a>
                </li>
              )}
            </ul>
          </div>

          <form onSubmit={handleSend} className="bg-white/5 p-6 rounded-lg">
            <h3 className="text-lg font-semibold">Send a message</h3>
            <div className="mt-4 space-y-3">
              <input
                className="w-full px-3 py-2 bg-transparent border border-white/10 rounded"
                placeholder="Your name"
                value={message.name}
                onChange={(e) => setMessage((m) => ({ ...m, name: e.target.value }))}
                required
              />
              <input
                className="w-full px-3 py-2 bg-transparent border border-white/10 rounded"
                placeholder="Your email"
                type="email"
                value={message.email}
                onChange={(e) => setMessage((m) => ({ ...m, email: e.target.value }))}
                required
              />
              <textarea
                className="w-full px-3 py-2 bg-transparent border border-white/10 rounded h-28"
                placeholder="Message"
                value={message.text}
                onChange={(e) => setMessage((m) => ({ ...m, text: e.target.value }))}
                required
              />
              <div className="flex items-center gap-3">
                <button type="submit" className="px-4 py-2 bg-indigo-600 rounded-md">Send</button>
                {sent && <span className="text-sm text-green-400">Message sent</span>}
              </div>
            </div>
          </form>
        </section>

        {/* Image modal */}
        {selected && (
          <div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
            onClick={() => setSelected(null)}
          >
            <div className="bg-slate-900 rounded-lg overflow-hidden max-w-3xl w-full">
              <img src={selected.image} alt={selected.title} className="w-full h-96 object-cover" />
              <div className="p-4">
                <div className="font-semibold text-lg">{selected.title}</div>
                <div className="text-white/80 mt-2">{selected.description}</div>
                <div className="mt-3 text-sm text-white/60">คลิกเพื่อปิด</div>
              </div>
            </div>
          </div>
        )}
      </div>

     

    </div>
  );
}