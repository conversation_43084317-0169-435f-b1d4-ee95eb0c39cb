import { Code, Palette, <PERSON>, <PERSON> } from "lucide-react";
import { EditProfileDialog } from "./EditProfileDialog";
import { useProfileData } from "./hooks/userProfileData";
import { But<PERSON> } from "@/components/ui/button";

export const AboutSection = () => {
  const { profileData, updateProfileData } = useProfileData();
  const highlights = [
    {
      icon: Code,
      title: "Clean Code",
      description: "Writing maintainable, scalable code that stands the test of time"
    },
    {
      icon: Palette,
      title: "Design-First",
      description: "Creating beautiful interfaces with attention to every detail"
    },
    {
      icon: Rocket,
      title: "Performance",
      description: "Optimizing for speed and efficiency in every project"
    },
    {
      icon: Users,
      title: "Collaboration",
      description: "Working seamlessly with teams to deliver exceptional results"
    }
  ];

  return (
    <section id="about" className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16 animate-fade-in">
          <div className="flex items-center justify-center gap-4 mb-6">
            <h2 className="text-4xl lg:text-5xl font-bold">
              About <span className="gradient-text">Me</span>
            </h2>
            <EditProfileDialog 
              initialData={profileData} 
              onSave={updateProfileData}
              trigger={
                <Button variant="ghost" size="sm" className="opacity-70 hover:opacity-100">
                  ✏️
                </Button>
              }
            />
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {profileData.bio}
          </p>
        </div>

        <div className="container mx-auto px-6">
          {/* Story */}
          <div className="animate-slide-up">
            <div className="glass-card p-8">
              <h3 className="text-2xl font-semibold mb-6 text-primary-glow">My Journey</h3>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  Started my journey as a self-taught developer, driven by curiosity and 
                  passion for technology. What began as building simple websites evolved 
                  into creating complex applications used by thousands.
                </p>
                <p>
                  I believe in the power of good design combined with solid engineering. 
                  Every project is an opportunity to push boundaries and create something 
                  meaningful that makes a real difference.
                </p>
                <p>
                  When I'm not coding, you'll find me exploring new technologies, 
                  contributing to open source projects, or mentoring upcoming developers.
                </p>
              </div>
            </div>
          </div>

          {/* Highlights */}
          
          {/*
          <div className="grid sm:grid-cols-2 gap-6 animate-slide-up">
            {highlights.map((item, index) => (
              <div 
                key={index}
                className="glass-card p-6 hover:border-primary/50 transition-all duration-300 group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="w-12 h-12 rounded-xl bg-primary/20 flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-all duration-300">
                  <item.icon className="h-6 w-6 text-primary" />
                </div>
                <h4 className="text-lg font-semibold mb-2">{item.title}</h4>
                <p className="text-muted-foreground text-sm">{item.description}</p>
              </div>
            ))}
          </div>
          */}

        </div>
      </div>
    </section>
  );
};