import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, MessageCircle, Phone, MapPin, Github, Linkedin, Twitter } from "lucide-react";
import { EditProfileDialog } from "./EditProfileDialog";
import { useProfileData } from "./hooks/userProfileData";

export const ContactSection = () => {
  const { profileData, updateProfileData } = useProfileData();
  
  const contactInfo = [
    {
      icon: Mail,
      title: "Email",
      value: profileData.email,
      link: `mailto:${profileData.email}`
    },
    {
      icon: Phone,
      title: "Phone",
      value: profileData.phone,
      link: `tel:${profileData.phone.replace(/\D/g, '')}`
    },
    {
      icon: MapPin,
      title: "Location",
      value: profileData.location,
      link: "#"
    }
  ];

  const socialLinks = [
    { icon: Gith<PERSON>, url: profileData.github, label: "GitHub" },
    { icon: Linkedin, url: profileData.linkedin, label: "LinkedIn" },
    { icon: Twitter, url: profileData.twitter, label: "Twitter" }
  ];

  return (
    <section id="contact" className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16 animate-fade-in">
          <div className="flex items-center justify-center gap-4 mb-6">
            <h2 className="text-4xl lg:text-5xl font-bold">
              Get In <span className="gradient-text">Touch</span>
            </h2>
            <EditProfileDialog 
              initialData={profileData} 
              onSave={updateProfileData}
              trigger={
                <Button variant="ghost" size="sm" className="opacity-70 hover:opacity-100">
                  ✏️
                </Button>
              }
            />
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Ready to start your next project? Let's discuss how we can work together 
            to bring your ideas to life.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Form */}
          <div className="animate-slide-up">
            <div className="glass-card p-8">
              <div className="flex items-center gap-3 mb-6">
                <MessageCircle className="h-6 w-6 text-primary" />
                <h3 className="text-2xl font-semibold">Send Message</h3>
              </div>
              
              <form className="space-y-6">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">First Name</label>
                    <Input placeholder="John" className="bg-secondary/50 border-glass-border" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Last Name</label>
                    <Input placeholder="Doe" className="bg-secondary/50 border-glass-border" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Email</label>
                  <Input type="email" placeholder="<EMAIL>" className="bg-secondary/50 border-glass-border" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Subject</label>
                  <Input placeholder="Project Discussion" className="bg-secondary/50 border-glass-border" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Message</label>
                  <Textarea 
                    placeholder="Tell me about your project..."
                    rows={5}
                    className="bg-secondary/50 border-glass-border resize-none"
                  />
                </div>
                
                <Button 
                  type="submit" 
                  size="lg" 
                  className="w-full hero-glow bg-primary hover:bg-primary-glow transition-all duration-300"
                >
                  Send Message
                </Button>
              </form>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-8 animate-slide-up" style={{ animationDelay: '0.2s' }}>
            {/* Contact Methods */}
            <div className="glass-card p-8">
              <h3 className="text-2xl font-semibold mb-6">Contact Information</h3>
              <div className="space-y-4">
                {contactInfo.map((info, index) => (
                  <a 
                    key={index}
                    href={info.link}
                    className="flex items-center gap-4 p-4 rounded-lg hover:bg-primary/10 transition-all duration-300 group"
                  >
                    <div className="w-12 h-12 rounded-xl bg-primary/20 flex items-center justify-center group-hover:bg-primary/30 transition-all duration-300">
                      <info.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{info.title}</p>
                      <p className="text-muted-foreground">{info.value}</p>
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Social Links */}
            <div className="glass-card p-8">
              <h3 className="text-2xl font-semibold mb-6">Follow Me</h3>
              <div className="flex gap-4">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    aria-label={social.label}
                    className="w-12 h-12 rounded-xl bg-primary/20 flex items-center justify-center hover:bg-primary hover:text-primary-foreground transition-all duration-300 group"
                  >
                    <social.icon className="h-5 w-5" />
                  </a>
                ))}
              </div>
            </div>

            {/* Call to Action */}
            <div className="glass-card p-8 bg-gradient-to-r from-primary/10 to-accent/10">
              <h4 className="text-xl font-semibold mb-3">Ready to Work Together?</h4>
              <p className="text-muted-foreground mb-4">
                Let's create something amazing together. I'm always excited to 
                take on new challenges and collaborate with passionate teams.
              </p>
              <Button variant="outline" className="border-primary bg-outline text-primary hover:bg-primary hover:text-primary-foreground">
                Schedule a Call
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};