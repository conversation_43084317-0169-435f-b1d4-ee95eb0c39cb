import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Slider } from "@/components/ui/slider";
import { Edit3, Save, X, Plus, Trash2 } from "lucide-react";

interface Skill {
  name: string;
  level: number;
}

interface SkillCategory {
  title: string;
  skills: Skill[];
}

interface EditSkillsDialogProps {
  initialSkills: SkillCategory[];
  onSave: (skills: SkillCategory[]) => void;
  trigger?: React.ReactNode;
}

export const EditSkillsDialog = ({ initialSkills, onSave, trigger }: EditSkillsDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [skillCategories, setSkillCategories] = useState<SkillCategory[]>(initialSkills);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onSave(skillCategories);
      setIsOpen(false);
      
      toast({
        title: "Skills Updated",
        description: "Your skills have been successfully updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update skills. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateSkill = (categoryIndex: number, skillIndex: number, field: 'name' | 'level', value: string | number) => {
    setSkillCategories(prev => 
      prev.map((category, catIdx) => 
        catIdx === categoryIndex 
          ? {
              ...category,
              skills: category.skills.map((skill, skillIdx) =>
                skillIdx === skillIndex
                  ? { ...skill, [field]: value }
                  : skill
              )
            }
          : category
      )
    );
  };

  const addSkill = (categoryIndex: number) => {
    setSkillCategories(prev =>
      prev.map((category, catIdx) =>
        catIdx === categoryIndex
          ? {
              ...category,
              skills: [...category.skills, { name: "", level: 50 }]
            }
          : category
      )
    );
  };

  const removeSkill = (categoryIndex: number, skillIndex: number) => {
    setSkillCategories(prev =>
      prev.map((category, catIdx) =>
        catIdx === categoryIndex
          ? {
              ...category,
              skills: category.skills.filter((_, skillIdx) => skillIdx !== skillIndex)
            }
          : category
      )
    );
  };

  const updateCategoryTitle = (categoryIndex: number, title: string) => {
    setSkillCategories(prev =>
      prev.map((category, catIdx) =>
        catIdx === categoryIndex
          ? { ...category, title }
          : category
      )
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="gap-2">
            <Edit3 className="h-4 w-4" />
            Edit Skills
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Edit Skills & Expertise
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {skillCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-4 p-4 border rounded-lg">
              <div className="space-y-2">
                <Label htmlFor={`category-${categoryIndex}`}>Category Name</Label>
                <Input
                  id={`category-${categoryIndex}`}
                  value={category.title}
                  onChange={(e) => updateCategoryTitle(categoryIndex, e.target.value)}
                  placeholder="e.g. Frontend Development"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Skills</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addSkill(categoryIndex)}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    Add Skill
                  </Button>
                </div>

                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="grid grid-cols-12 gap-3 items-end">
                    <div className="col-span-4">
                      <Label htmlFor={`skill-${categoryIndex}-${skillIndex}`}>Skill Name</Label>
                      <Input
                        id={`skill-${categoryIndex}-${skillIndex}`}
                        value={skill.name}
                        onChange={(e) => updateSkill(categoryIndex, skillIndex, 'name', e.target.value)}
                        placeholder="e.g. React"
                      />
                    </div>
                    
                    <div className="col-span-6">
                      <Label>Proficiency: {skill.level}%</Label>
                      <Slider
                        value={[skill.level]}
                        onValueChange={(value) => updateSkill(categoryIndex, skillIndex, 'level', value[0])}
                        min={0}
                        max={100}
                        step={5}
                        className="mt-2"
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeSkill(categoryIndex, skillIndex)}
                        className="w-full"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="gap-2"
          >
            <Save className="h-4 w-4" />
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};