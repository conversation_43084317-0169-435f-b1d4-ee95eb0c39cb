import { Calendar, MapPin } from "lucide-react";

export const ExperienceSection = () => {
  const experiences = [
    {
      title: "Senior Full Stack Developer",
      company: "TechFlow Solutions",
      location: "San Francisco, CA",
      period: "2022 - Present",
      description: "Leading development of enterprise-scale applications using React, Node.js, and cloud technologies. Mentoring junior developers and architecting scalable solutions.",
      achievements: [
        "Reduced application load time by 60% through optimization",
        "Led team of 5 developers on critical projects",
        "Implemented CI/CD pipeline increasing deployment efficiency by 80%"
      ]
    },
    {
      title: "Frontend Developer",
      company: "DigitalCraft Agency",
      location: "Seattle, WA", 
      period: "2020 - 2022",
      description: "Developed responsive web applications and collaborated with designers to create pixel-perfect user interfaces for various clients.",
      achievements: [
        "Built 15+ responsive websites for diverse clients",
        "Improved client satisfaction scores by 40%",
        "Introduced modern development practices and tools"
      ]
    },
    {
      title: "Junior Web Developer",
      company: "StartupLab",
      location: "Portland, OR",
      period: "2019 - 2020", 
      description: "Started my professional journey building web applications with JavaScript, HTML, and CSS. Gained valuable experience in agile development.",
      achievements: [
        "Contributed to 3 successful product launches",
        "Learned modern frameworks and best practices",
        "Collaborated with cross-functional teams"
      ]
    }
  ];

  return (
    <section id="experience" className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Work <span className="gradient-text">Experience</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A journey through impactful roles and meaningful contributions to innovative projects.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {experiences.map((exp, index) => (
            <div 
              key={index}
              className="relative animate-slide-up"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Timeline line */}
              {index !== experiences.length - 1 && (
                <div className="absolute left-6 top-16 w-0.5 h-full bg-primary/30"></div>
              )}
              
              {/* Timeline dot */}
              <div className="absolute left-4 top-6 w-4 h-4 rounded-full bg-primary border-4 border-background"></div>
              
              {/* Content */}
              <div className="ml-16 mb-12">
                <div className="glass-card p-8">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-primary-glow">{exp.title}</h3>
                      <h4 className="text-lg font-medium mt-1">{exp.company}</h4>
                    </div>
                    <div className="flex flex-col lg:items-end mt-2 lg:mt-0 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {exp.period}
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        <MapPin className="h-4 w-4" />
                        {exp.location}
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-muted-foreground mb-4">{exp.description}</p>
                  
                  <div className="space-y-2">
                    <h5 className="font-medium text-sm">Key Achievements:</h5>
                    <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};