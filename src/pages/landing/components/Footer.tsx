import { Gith<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Heart } from "lucide-react";

export const Footer = () => {
  const socialLinks = [
    { icon: Github, url: "#", label: "GitHub" },
    { icon: Linkedin, url: "#", label: "LinkedIn" },
    { icon: Twitter, url: "#", label: "Twitter" }
  ];

  return (
    <footer className="bg-secondary/50 border-t border-glass-border/50">
      <div className="container mx-auto px-6 py-12">
        <div className="flex flex-col md:flex-row items-center justify-between">
          {/* Logo & Info */}
          <div className="text-center md:text-left mb-6 md:mb-0">
            <div className="text-2xl font-bold gradient-text mb-2"><PERSON></div>
            <p className="text-muted-foreground">Full Stack Developer & UI/UX Designer</p>
          </div>

          {/* Social Links */}
          <div className="flex items-center gap-4 mb-6 md:mb-0">
            {socialLinks.map((social, index) => (
              <a
                key={index}
                href={social.url}
                aria-label={social.label}
                className="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center hover:bg-primary hover:text-primary-foreground transition-all duration-300 group"
              >
                <social.icon className="h-5 w-5" />
              </a>
            ))}
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-glass-border/30 mt-8 pt-8 text-center">
          <p className="text-muted-foreground flex items-center justify-center gap-2">
            Made with <Heart className="h-4 w-4 text-red-500" /> by Alex Johnson
            <span className="mx-2">•</span>
            © 2024 All rights reserved
          </p>
        </div>
      </div>
    </footer>
  );
};