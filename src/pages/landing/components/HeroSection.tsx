import { useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown, Download, Mail } from "lucide-react";
import heroPortrait from "../assets/hero-portrait.jpg";
import { EditProfileDialog } from "./EditProfileDialog";
import { LandingContext } from "@/contexts/LandingContext";

export const HeroSection = () => {  
  const { pageData, updateProfile } = useContext(LandingContext);

  const handleSave = async () => {
    const formDataProfile = new FormData();
    formDataProfile.append('name', pageData.name);
    formDataProfile.append('title', pageData.title);
    formDataProfile.append('bio', pageData.bio);

    console.log('Updating profile:', formDataProfile);

    //updateProfile(formDataProfile);
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-accent/10 to-background"></div>
      {/* Floating elements */}
      <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-primary/20 animate-float"></div>
      <div className="absolute bottom-32 right-20 w-32 h-32 rounded-full bg-accent/20 animate-float" style={{ animationDelay: '2s' }}></div>
      <div className="container mx-auto px-6 relative z-10">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Text Content */}
          <div className="flex-1 text-center lg:text-left animate-fade-in">
            <div className="mb-6">
              <div className="flex items-center justify-center lg:justify-start gap-4 mb-4">
                <span className="text-primary-glow text-lg font-medium tracking-wide">Hello, I'm</span>
                <EditProfileDialog
                  initialData={pageData}
                  onSave={handleSave}
                  trigger={
                    <Button variant="ghost" size="sm" className="opacity-70 hover:opacity-100">
                      ✏️
                    </Button>
                  }
                />
              </div>
              <h1 className="text-5xl lg:text-7xl font-bold mt-2 mb-4">
                <span className="gradient-text">{pageData.name}</span>
              </h1>
              <h2 className="text-2xl lg:text-3xl text-muted-foreground font-light">
                {pageData.title}
              </h2>
            </div>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl">
              {pageData.bio}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button size="lg" className="hero-glow bg-primary hover:bg-primary-glow transition-all duration-300">
                <Mail className="mr-2 h-5 w-5" />
                Get In Touch
              </Button>
              <Button variant="outline" size="lg" className="border-primary bg-outline text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-300">
                <Download className="mr-2 h-5 w-5" />
                Download CV
              </Button>
            </div>
          </div>
          {/* Profile Image */}
          <div className="flex-1 flex justify-center animate-slide-up">
            <div className="relative">
              <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden glass-card p-2">
                <img
                  src={heroPortrait}
                  alt={`${pageData.name} - ${pageData.title}`}
                  className="w-full h-full object-cover rounded-full"
                />
              </div>
              <div className="absolute -inset-4 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-2xl -z-10"></div>
            </div>
          </div>
        </div>
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ArrowDown className="text-primary h-6 w-6" />
        </div>
      </div>
    </section>
  );
};