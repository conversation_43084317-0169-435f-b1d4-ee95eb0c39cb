import { Progress } from "@/components/ui/progress";
import { EditSkillsDialog } from "./EditSkillsDialog";
import { useProfileData } from "./hooks/userProfileData";
import { Button } from "@/components/ui/button";

export const SkillsSection = () => {
  const { skillCategories, updateSkillCategories } = useProfileData();

  return (
    <section id="skills" className="py-20 bg-secondary/30">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16 animate-fade-in">
          <div className="flex items-center justify-center gap-4 mb-6">
            <h2 className="text-4xl lg:text-5xl font-bold">
              Technical <span className="gradient-text">Skills</span>
            </h2>
            <EditSkillsDialog 
              initialSkills={skillCategories} 
              onSave={updateSkillCategories}
              trigger={
                <Button variant="ghost" size="sm" className="opacity-70 hover:opacity-100">
                  ✏️
                </Button>
              }
            />
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Constantly learning and adapting to new technologies to deliver cutting-edge solutions.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <div 
              key={categoryIndex}
              className="glass-card p-8 animate-slide-up"
              style={{ animationDelay: `${categoryIndex * 0.2}s` }}
            >
              <h3 className="text-xl font-semibold mb-6 text-primary-glow">
                {category.title}
              </h3>
              <div className="space-y-6">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{skill.name}</span>
                      <span className="text-xs text-muted-foreground">{skill.level}%</span>
                    </div>
                    <Progress 
                      value={skill.level} 
                      className="h-2 bg-muted/30"
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};