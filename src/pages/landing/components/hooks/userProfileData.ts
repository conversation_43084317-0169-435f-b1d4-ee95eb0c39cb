import { useState, useEffect } from 'react';

export interface ProfileData {
  name: string;
  title: string;
  bio: string;
  email: string;
  phone: string;
  location: string;
  github: string;
  linkedin: string;
  twitter: string;
}

export interface Skill {
  name: string;
  level: number;
}

export interface SkillCategory {
  title: string;
  skills: Skill[];
}

// Default data
const defaultProfileData: ProfileData = {
  name: "<PERSON>",
  title: "Full Stack Developer",
  bio: "With over 5 years of experience in web development, I've helped businesses transform their ideas into powerful digital solutions.",
  email: "<EMAIL>",
  phone: "+****************",
  location: "San Francisco, CA",
  github: "https://github.com/alexjohnson",
  linkedin: "https://linkedin.com/in/alexjohnson",
  twitter: "https://twitter.com/alexjohnson"
};

const defaultSkillCategories: SkillCategory[] = [
  {
    title: "Frontend Development",
    skills: [
      { name: "React/Next.js", level: 95 },
      { name: "TypeScript", level: 90 },
      { name: "Tailwind CSS", level: 92 },
      { name: "Vue.js", level: 85 }
    ]
  },
  {
    title: "Backend Development", 
    skills: [
      { name: "Node.js", level: 88 },
      { name: "Python", level: 82 },
      { name: "PostgreSQL", level: 85 },
      { name: "MongoDB", level: 80 }
    ]
  },
  {
    title: "Tools & Technologies",
    skills: [
      { name: "Git/GitHub", level: 93 },
      { name: "Docker", level: 78 },
      { name: "AWS", level: 75 },
      { name: "Figma", level: 87 }
    ]
  }
];

export const useProfileData = () => {
  const [profileData, setProfileData] = useState<ProfileData>(defaultProfileData);
  const [skillCategories, setSkillCategories] = useState<SkillCategory[]>(defaultSkillCategories);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedProfile = localStorage.getItem('profileData');
    const savedSkills = localStorage.getItem('skillCategories');

    if (savedProfile) {
      try {
        setProfileData(JSON.parse(savedProfile));
      } catch (error) {
        console.error('Error loading profile data:', error);
      }
    }

    if (savedSkills) {
      try {
        setSkillCategories(JSON.parse(savedSkills));
      } catch (error) {
        console.error('Error loading skills data:', error);
      }
    }
  }, []);

  const updateProfileData = (newData: ProfileData) => {
    setProfileData(newData);
    localStorage.setItem('profileData', JSON.stringify(newData));
  };

  const updateSkillCategories = (newSkills: SkillCategory[]) => {
    setSkillCategories(newSkills);
    localStorage.setItem('skillCategories', JSON.stringify(newSkills));
  };

  return {
    profileData,
    skillCategories,
    updateProfileData,
    updateSkillCategories
  };
};