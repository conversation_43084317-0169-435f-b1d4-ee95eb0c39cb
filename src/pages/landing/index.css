@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Resume Portfolio Design System */

@layer base {
   :root {
      /* Core Brand Colors */
      --background: 220 25% 6%;
      --foreground: 210 40% 98%;

      /* Professional Blues & Purples */
      --primary: 225 75% 55%;
      --primary-foreground: 210 40% 98%;
      --primary-glow: 225 85% 65%;

      --secondary: 220 20% 15%;
      --secondary-foreground: 210 40% 98%;

      --accent: 270 75% 60%;
      --accent-foreground: 210 40% 98%;

      /* Card System */
      --card: 220 25% 8%;
      --card-foreground: 210 40% 98%;
      --card-border: 220 15% 20%;

      /* Glass Effect */
      --glass: 220 25% 10%;
      --glass-border: 220 15% 25%;

      --muted: 220 15% 25%;
      --muted-foreground: 215 15% 70%;

      --border: 220 15% 20%;
      --input: 220 20% 15%;
      --ring: 225 75% 55%;

      /* Gradients */
      --gradient-primary: linear-gradient(135deg, hsl(225 75% 55%), hsl(270 75% 60%));
      --gradient-hero: linear-gradient(135deg, hsl(225 75% 55% / 0.1), hsl(270 75% 60% / 0.1));
      --gradient-card: linear-gradient(135deg, hsl(220 25% 8% / 0.8), hsl(220 20% 15% / 0.8));

      /* Shadows & Effects */
      --shadow-glow: 0 20px 40px -12px hsl(225 75% 55% / 0.3);
      --shadow-card: 0 10px 30px -10px hsl(220 25% 6% / 0.5);

      /* Animations */
      --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

      --radius: 1rem;

      --sidebar-background: 0 0% 98%;

      --sidebar-foreground: 240 5.3% 26.1%;

      --sidebar-primary: 240 5.9% 10%;

      --sidebar-primary-foreground: 0 0% 98%;

      --sidebar-accent: 240 4.8% 95.9%;

      --sidebar-accent-foreground: 240 5.9% 10%;

      --sidebar-border: 220 13% 91%;

      --sidebar-ring: 217.2 91.2% 59.8%;
   }

   .dark {
      --background: 222.2 84% 4.9%;
      --foreground: 210 40% 98%;

      --card: 222.2 84% 4.9%;
      --card-foreground: 210 40% 98%;

      --popover: 222.2 84% 4.9%;
      --popover-foreground: 210 40% 98%;

      --primary: 210 40% 98%;
      --primary-foreground: 222.2 47.4% 11.2%;

      --secondary: 217.2 32.6% 17.5%;
      --secondary-foreground: 210 40% 98%;

      --muted: 217.2 32.6% 17.5%;
      --muted-foreground: 215 20.2% 65.1%;

      --accent: 217.2 32.6% 17.5%;
      --accent-foreground: 210 40% 98%;

      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 40% 98%;

      --border: 217.2 32.6% 17.5%;
      --input: 217.2 32.6% 17.5%;
      --ring: 212.7 26.8% 83.9%;
      --sidebar-background: 240 5.9% 10%;
      --sidebar-foreground: 240 4.8% 95.9%;
      --sidebar-primary: 224.3 76.3% 48%;
      --sidebar-primary-foreground: 0 0% 100%;
      --sidebar-accent: 240 3.7% 15.9%;
      --sidebar-accent-foreground: 240 4.8% 95.9%;
      --sidebar-border: 240 3.7% 15.9%;
      --sidebar-ring: 217.2 91.2% 59.8%;
   }
}

@layer base {
   * {
      @apply border-border;
   }

   body {
      @apply bg-background text-foreground font-sans;
      scroll-behavior: smooth;
   }

   html {
      scroll-behavior: smooth;
   }
}

@layer components {
   .glass-card {
      @apply bg-glass-50 backdrop-blur-xl border border-glass-border-50 rounded-2xl;
      box-shadow: var(--shadow-card);
   }

   .bg-glass-50 {
      background-color: rgba(var(--glass), 0.5);
   }
  
   .border-glass-border-50 {
      box-shadow: rgba(var(--glass-border), 0.5);
   }
  
   .text-primary-glow {
      color: hsl(var(--primary-glow));
   }

   .gradient-text {
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
   }

   .hero-glow {
      box-shadow: var(--shadow-glow);
   }
  
   .animate-fade-in {
      animation: fadeIn 0.6s ease-out forwards;
   }

   .animate-slide-up {
      animation: slideUp 0.8s ease-out forwards;
   }

   .animate-float {
      animation: float 6s ease-in-out infinite;
   }

}

@keyframes fadeIn {
   from {
      opacity: 0;
      transform: translateY(20px);
   }

   to {
      opacity: 1;
      transform: translateY(0);
   }
}

@keyframes slideUp {
   from {
      opacity: 0;
      transform: translateY(40px);
   }

   to {
      opacity: 1;
      transform: translateY(0);
   }
}

@keyframes float {

   0%,
   100% {
      transform: translateY(0px);
   }

   50% {
      transform: translateY(-10px);
   }
}