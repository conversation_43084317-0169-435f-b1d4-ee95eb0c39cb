import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function ResetPasswordPage() {
   const [searchParams] = useSearchParams();
   const token = searchParams.get("token");

   const [password, setPassword] = useState("");
   const [confirmPassword, setConfirmPassword] = useState("");
   const [loading, setLoading] = useState(false);
   const [message, setMessage] = useState<string | null>(null);
   const [error, setError] = useState<string | null>(null);

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setMessage(null);
      setError(null);

      if (!token) {
         setError("Token not found. Please use the link from your email again.");
         return;
      }

      if (password !== confirmPassword) {
         setError("Passwords do not match.");
         return;
      }

      setLoading(true);
      try {
         const res = await fetch("http://localhost:3000/auth/reset/confirm", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ token, newPassword: password }),
         });

         const data = await res.json();
         if (res.ok && data.ok) {
            setMessage("Your password has been successfully reset ✅");
         } else {
            setError(data.error || "Something went wrong. Please try again.");
         }
      } catch (err) {
         console.error(err);
         setError("Unable to connect to the server.");
      } finally {
         setLoading(false);
      }
   };

   return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
         <div className="container mx-auto px-4 py-8">

            {/* Hero Section */}
            <div className="text-center mb-16">
               <div className="flex items-center justify-center mb-6">
                  <div className="bg-white backdrop-blur-lg rounded-full p-4">
                     <a href="/">
                        <img src="./images/logoLinkHub.png" alt="Logo" className="w-48 h-auto" />
                     </a>
                  </div>
               </div>
            </div>

            <Card className="w-full max-w-md mx-auto backdrop-blur-lg bg-white/10 border-white/20">

               <CardHeader className="text-center">
                  <CardTitle className="text-2xl font-bold text-white">
                     Reset Password
                  </CardTitle>
                  <CardDescription className="text-white/80">
                     Enter your new password
                  </CardDescription>
               </CardHeader>

               <CardContent>
                  {message ? (
                     <div className="p-3 mb-4 text-green-700 bg-green-100 rounded-lg">
                        {message}
                     </div>
                  ) : (
                     <form onSubmit={handleSubmit} className="space-y-4">
                        {error && (
                           <div className="p-3 text-red-700 bg-red-100 rounded-lg">
                              {error}
                           </div>
                        )}
                        <div>
                           <Input
                              type="password"
                              placeholder="New Password"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
                              minLength={8}
                              required
                           />
                        </div>
                        <div>
                           <Input
                              type="password"
                              placeholder="Confirm Password"
                              value={confirmPassword}
                              onChange={(e) => setConfirmPassword(e.target.value)}
                              className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
                              minLength={8}
                              required
                           />                           
                        </div>

                        <Button 
                           type="submit"             
                           className="w-full bg-white text-purple-700 hover:bg-white/90 font-semibold"
                           disabled={loading}
                        >
                           {loading ? "Updating..." : "Confirm"}
                        </Button>
                        
                     </form>
                  )}

               </CardContent>



            </Card>


         </div>
      </div>
   );
}
